#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <fstream>
#include <signal.h>
#include <unistd.h>
#include <cstring>
#include <atomic>
#include <memory>
#include "framework/unix_socket.h"
#include "ipc/ipc_msg.h"
#include "xop/RtspServer.h"
#include "net/Timer.h"
#include "slc_capture.h"

using namespace std;

// 全局变量声明
SlcCap* g_cap = nullptr;

class CaptureControlTest {
private:
    UnixSocketServerStream* control_server_;
    UnixSocketClientDgram* nalu_client_;
    std::string control_socket_path_;
    std::string nalu_socket_path_;
    std::atomic<bool> running_;
    std::thread input_thread_;
    xop::MediaSessionId session_id_;
    xop::RtspServer* rtsp_server_;
    std::shared_ptr<xop::EventLoop> event_loop_;
    std::shared_ptr<xop::RtspServer> server_;
public:
    CaptureControlTest(const std::string& control_path = "/run/ctrl.sock",
                      const std::string& nalu_path = "/run/nalu.sock")
        : control_server_(nullptr), nalu_client_(nullptr),
          control_socket_path_(control_path), nalu_socket_path_(nalu_path),
          running_(true), rtsp_server_(nullptr) {
    }
    
    ~CaptureControlTest() {
        stop();
    }
    
    bool start() {

        // 启动rtspServer
        std::string suffix = "live";
        std::string ip = "127.0.0.1";
        std::string port = "554";
        std::string rtsp_url = "rtsp://" + ip + ":" + port + "/" + suffix;

        event_loop_.reset(new xop::EventLoop());
        server_ = xop::RtspServer::Create(event_loop_.get());
        rtsp_server_ = server_.get();

        if (!server_->Start("0.0.0.0", atoi(port.c_str()))) {
          printf("RTSP Server listen on %s failed.\n", port.c_str());
          return false;
        }

      #ifdef AUTH_CONFIG
        server_->SetAuthConfig("-_-", "admin", "12345");
      #endif

        xop::MediaSession *session = xop::MediaSession::CreateNew("live");
        session->AddSource(xop::channel_0, xop::H264Source::CreateNew());
        //session->StartMulticast();
        session->AddNotifyConnectedCallback([](xop::MediaSessionId sessionId, string peer_ip, uint16_t peer_port) {
          printf("RTSP client connect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
        });

        session->AddNotifyDisconnectedCallback([](xop::MediaSessionId sessionId, string peer_ip, uint16_t peer_port) {
          printf("RTSP client disconnect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
        });

        session_id_ = server_->AddSession(session);

        std::cout << "Play URL: " << rtsp_url << std::endl;

        // 启动控制服务器
        if (!startControlServer()) {
            return false;
        }
        std::cout<<"wait for yaslc start"<<std::endl;
        // 连接到NALU客户端
        while (!connectNaluClient()) {
            sleep(1);
        }
        
        // 启动输入处理线程
        input_thread_ = std::thread(&CaptureControlTest::inputLoop, this);
        
        std::cout << "Test program started successfully!" << std::endl;
        std::cout << "Commands:" << std::endl;
        std::cout << "  s - Start capture (simple)" << std::endl;
        std::cout << "  S - Start capture with info" << std::endl;
        std::cout << "  t - Stop capture" << std::endl;
        std::cout << "  q - Quit" << std::endl;
        
        return true;
    }
    
    void stop() {
        running_ = false;

        if (input_thread_.joinable()) {
            input_thread_.join();
        }

        if (control_server_) {
            control_server_->stop();
            delete control_server_;
            control_server_ = nullptr;
        }

        if (nalu_client_) {
            nalu_client_->stop_async_receive();
            nalu_client_->disconnect();
            delete nalu_client_;
            nalu_client_ = nullptr;
        }

        std::cout << "Test program stopped" << std::endl;
    }

    bool isRunning() const {
        return running_;
    }
    
private:
    bool startControlServer() {
        control_server_ = new UnixSocketServerStream(control_socket_path_);
        
        // 设置消息处理回调
        control_server_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
            switch (msg_type) {
                case IPC_MSG_HELLO:
                    if (data && length == sizeof(ipc_msg_hello)) {
                        const ipc_msg_hello* hello = reinterpret_cast<const ipc_msg_hello*>(data);
                        std::cout << "Received HELLO message: DPI version=" << (int)hello->dpi_version
                                  << ", Plugin version=" << (int)hello->plugin_version
                                  << ", Plugin path=" << hello->plugin_path << std::endl;
                    }
                    break;
                case IPC_MSG_STREAM_DETECTED:
                    if (data && length == sizeof(ipc_msg_stream_detected)) {
                        const ipc_msg_stream_detected* stream = reinterpret_cast<const ipc_msg_stream_detected*>(data);
                        std::cout << "Received STREAM_DETECTED message: StreamID=" << stream->streamID
                                  << ", Protocol=" << stream->proto
                                  << ", Flag=" << stream->flag << std::endl;
                    }
                    break;
                case IPC_MSG_DUMP_START:
                    std::cout << "Received dump start notification" << std::endl;
                    break;
                case IPC_MSG_DUMP_DONE:
                    std::cout << "Received dump done notification" << std::endl;
                    break;
                case IPC_MSG_HEART_BEAT:
                    // 回复心跳
                    std::cout << "Received heartbeat from client" << std::endl;
                    control_server_->send_message(client_fd, IPC_MSG_HEART_BEAT, nullptr, 0);
                    break;
                default:
                    std::cout << "Control server received unknown message type: " << msg_type << std::endl;
                    break;
            }
        });
        
        if (!control_server_->start()) {
            std::cerr << "Failed to start control server on " << control_socket_path_ << std::endl;
            delete control_server_;
            control_server_ = nullptr;
            return false;
        }
        
        std::cout << "Control server started on " << control_socket_path_ << std::endl;
        return true;
    }
    
    bool connectNaluClient() {
        nalu_client_ = new UnixSocketClientDgram(nalu_socket_path_);
        
        // 设置消息处理回调
        nalu_client_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
            switch (msg_type) {
                case IPC_MSG_STREAM_NALU:
                std::cout<<"recv nalu message len"<< length<<std::endl;
                    if (data && length > 0) {
                        xop::AVFrame videoFrame = {0};
                        videoFrame.type = 0;
                        videoFrame.size = length;
                        videoFrame.timestamp = xop::H264Source::GetTimestamp();
                        videoFrame.buffer.reset(new uint8_t[videoFrame.size]);
                        memcpy(videoFrame.buffer.get(), data, videoFrame.size);
                        if (rtsp_server_) {
                            rtsp_server_->PushFrame(session_id_, xop::channel_0, videoFrame);
                        }
                    }
                    break;
                case IPC_MSG_HEART_BEAT:
                    // 心跳消息，不需要特殊处理
                    break;
                default:
                    std::cout << "NALU client received unknown message type: " << msg_type << std::endl;
                    break;
            }
        });
        
        // 尝试连接
        if (!nalu_client_->connect()) {
            std::cerr << "Failed to connect to NALU server on " << nalu_socket_path_ << std::endl;
            delete nalu_client_;
            nalu_client_ = nullptr;
            return false;
        }
        
        // 启动异步接收
        nalu_client_->start_async_receive();

        // 发送HELLO消息让服务器知道客户端地址
        ipc_msg_hello hello_msg;
        memset(&hello_msg, 0, sizeof(hello_msg));
        hello_msg.dpi_version = 1;
        hello_msg.plugin_version = 1;
        strncpy(hello_msg.plugin_path, "test_nalu_client", sizeof(hello_msg.plugin_path) - 1);

        if (!nalu_client_->send_message(IPC_MSG_HELLO, (const uint8_t*)&hello_msg, sizeof(hello_msg))) {
            std::cerr << "Failed to send HELLO message to NALU server" << std::endl;
            return false;
        } else {
            std::cout << "Sent HELLO message to NALU server" << std::endl;
        }

        std::cout << "Connected to NALU server on " << nalu_socket_path_ << std::endl;
        return true;
    }
    
    void inputLoop() {
        char input;
        while (running_) {
            std::cout << "> ";
            std::cin >> input;
            
            switch (input) {
                case 's':
                    sendCaptureCommand(true);
                    break;
                case 'S':
                    sendCaptureCommandWithInfo();
                    break;
                case 't':
                case 'T':
                    sendCaptureCommand(false);
                    break;
                case 'q':
                case 'Q':
                    running_ = false;
                    stop();
                    break;
                default:
                    std::cout << "Unknown command. Use 's' to start, 't' to stop, 'q' to quit." << std::endl;
                    break;
            }
        }
    }
    
    void sendCaptureCommand(bool start_capture) {
        if (!control_server_) {
            std::cerr << "Control server not available" << std::endl;
            return;
        }

        if (start_capture) {
            // 发送简单的抓包开始消息（使用默认参数）
            control_server_->broadcast_message(IPC_MSG_DUMP_START, nullptr, 0);
        } else {
            // 发送抓包停止消息
            control_server_->broadcast_message(IPC_MSG_DUMP_STOP, nullptr, 0);
        }

        std::cout << "Sent " << (start_capture ? "start" : "stop") << " capture command" << std::endl;
    }

    void sendCaptureCommandWithInfo() {
        if (!control_server_) {
            std::cerr << "Control server not available" << std::endl;
            return;
        }

        ipc_msg_dump_start msg;
        memset(&msg, 0, sizeof(msg));

        // 设置抓包参数
        msg.dump_size = 50 * 1024 * 1024;  // 50MB文件大小（转换为字节）
        strncpy(msg.brand, "TestBrand", sizeof(msg.brand) - 1);
        strncpy(msg.serial, "TestModel", sizeof(msg.serial) - 1);

        control_server_->broadcast_message(IPC_MSG_DUMP_START,
                                         reinterpret_cast<const uint8_t*>(&msg),
                                         sizeof(msg));

        std::cout << "Sent capture start command with info - Brand: " << msg.brand
                  << ", Serial: " << msg.serial
                  << ", FileSize: " << (msg.dump_size / (1024 * 1024)) << "MB" << std::endl;
    }
};

// 全局变量用于信号处理
CaptureControlTest* g_test = nullptr;

void signalHandler(int signum) {
  std::cout << "\nReceived signal " << signum << ", shutting down..." << std::endl;
  if (g_test) {
    g_test->stop();
  }
  exit(0);
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    std::string control_path = "/run/ctrl.sock";
    std::string nalu_path = "/run/nalu.sock";
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--control") == 0 && i + 1 < argc) {
            control_path = argv[i + 1];
            i++;
        } else if (strcmp(argv[i], "--nalu") == 0 && i + 1 < argc) {
            nalu_path = argv[i + 1];
            i++;
        } else if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
            std::cout << "Usage: " << argv[0] << " [OPTIONS]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --control <path>  Control socket path (default: /run/ctrl.sock)" << std::endl;
            std::cout << "  --nalu <path>     NALU socket path (default: /run/nalu.sock)" << std::endl;
            std::cout << "  --help, -h        Show this help" << std::endl;
            return 0;
        }
    }
    
    // 创建测试实例
    g_test = new CaptureControlTest(control_path, nalu_path);
    
    if (!g_test->start()) {
        std::cerr << "Failed to start test program" << std::endl;
        delete g_test;
        return 1;
    }
    
    // 等待程序结束
    while (g_test) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        if (!g_test->isRunning()) {
            break;
        }
    }
    
    delete g_test;
    g_test = nullptr;
    
    return 0;
}
