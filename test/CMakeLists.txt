cmake_minimum_required(VERSION 3.14)

# project
project(IPCFWDTest LANGUAGES C CXX VERSION 0.0.1)
option(USE_PKG_CONFIG_TO_LINK off)

set(CMAKE_C_STANDARD 17)


# 添加测试程序
add_executable(test_capture_control
  test_capture_control.cpp
  ../src/slc_capture.cpp
  $<TARGET_OBJECTS:framework_minimal>
  $<TARGET_OBJECTS:net>
  $<TARGET_OBJECTS:xop>
)
include_directories(
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/src/framework
  ${CMAKE_SOURCE_DIR}/src/net
  ${CMAKE_SOURCE_DIR}/src/xop
  ${CMAKE_SOURCE_DIR}/src/3rdpart/md5
  ${CMAKE_SOURCE_DIR}/include/glib-2.0/
)
set_target_properties(test_capture_control PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)

target_link_directories(test_capture_control PRIVATE
    ${CMAKE_SOURCE_DIR}/lib/amd64
)

target_link_libraries(test_capture_control
    iniparser
    pcap
    pthread
    tcp_rsm
    glib-2.0
    m
    iconv
    dl
)
