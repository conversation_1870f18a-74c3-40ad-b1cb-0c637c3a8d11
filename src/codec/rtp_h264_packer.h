#ifndef RTP_H264_PACKER_H
#define RTP_H264_PACKER_H

#include "slc_nalu.h"

#include <vector>
#include <stdint.h>
#include <ctime>
#include "slc_typedefs.h"

#include "slc_logger.h"
extern slc::Logger global_logger;
#define MAX_NALU_SLICE_LEN 1360
enum NaluType_e {
  NT_none = 0,                   //  "Undefined"
  NT_slice_nIDR = 1,             //  "NAL unit - Coded slice of a non-IDR picture"
  NT_slice_data_part_A = 2,      //  "NAL unit - Coded slice data partition A"
  NT_slice_data_part_B = 3,      //  "NAL unit - Coded slice data partition B"
  NT_slice_data_part_C = 4,      //  "NAL unit - Coded slice data partition C"
  NT_slice_IDR = 5,              //  "NAL unit - Coded slice of an IDR picture"
  NT_SEI = 6,                    //  "NAL unit - Supplemental enhancement information (SEI)"
  NT_SPS = 7,                    //  "NAL unit - Sequence parameter set"
  NT_PPS = 8,                    //  "NAL unit - Picture parameter set"
  NT_access_unit_delimiter = 9,  //  "NAL unit - Access unit delimiter"
  NT_end_of_seq = 10,            //  "NAL unit - End of sequence"
  NT_end_of_stream = 11,         //  "NAL unit - End of stream"
  NT_filter_data = 12,           //  "NAL unit - Filler data"
  NT_SPS_ext = 13,               //  "NAL unit - Sequence parameter set extension"
  NT_prefix = 14,                //  "NAL unit - Prefix"
  NT_sub_SPS = 15,               //  "NAL unit - Subset sequence parameter set"
  NT_resv_16 = 16,               //  "NAL unit - Reserved"
  NT_resv_17 = 17,               //  "NAL unit - Reserved"
  NT_resv_18 = 18,               //  "NAL unit - Reserved"
  NT_slice_aux = 19,             //  "NAL unit - Coded slice of an auxiliary coded picture without partitioning"
  NT_slice_ext = 20,             //  "NAL unit - Coded slice extension"
  NT_slice_ext_depth_view = 21,  //  "NAL unit - Coded slice extension for depth view components"
  NT_resv_22 = 22,               //  "NAL unit - Reserved"
  NT_resv_23 = 23,               //  "NAL unit - Reserved"
  NT_STAP_A = 24,                //  "Single-time aggregation packet A (STAP-A)"
  NT_STAP_B = 25,                //  "Single-time aggregation packet B (STAP-B)"
  NT_MTAP_16 = 26,               //  "Multi-time aggregation packet 16 (MTAP16)"
  NT_MTAP_24 = 27,               //  "Multi-time aggregation packet 24 (MTAP24)"
  NT_FU_A = 28,                  //  "Fragmentation unit A (FU-A)"
  NT_FU_B = 29,                  //  "Fragmentation unit B (FU-B)"
  NT_PACSI = 30,                 //  "NAL unit - Payload Content Scalability Information (PACSI)"
  NT_EAH = 31,                   //  "NAL unit - Extended NAL Header"
};

typedef int (*onGotNalu_callback_t)(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

// rtp h264 解包器
// 输入为 rtp 负载，输出为 nalu(回调)
class RtpH264Unpacker {
public:
  RtpH264Unpacker(onGotNalu_callback_t callback, void *userdata);

public:
  int enqueueRtpPayload(uint8_t *rtpPayload, int len);

private:
  int processSTAP_A(uint8_t *rtpPayload, int len);

  int processFU_A(uint8_t *rtpPayload, int len);

  // 重置FU-A状态，防止OOM
  void resetFuAState();

  // 检查NALU缓冲区大小是否超限
  bool isNaluBufferOverLimit() const;

private:
  onGotNalu_callback_t onGotNalu_func_;
  std::vector<uint8_t> naluBuff_;  // 存放一个 nulu 的 buff;
  void                *userdata_;

  // FU-A状态管理，防止OOM
  enum FuAState {
    FU_A_IDLE,      // 空闲状态，等待start
    FU_A_RECEIVING  // 正在接收FU-A片段
  } fuAState_;

  uint16_t lastRtpSeq_;           // 上一个RTP包的序列号
  bool     hasValidRtpSeq_;       // 是否有有效的RTP序列号

  // 安全限制
  static const size_t MAX_NALU_SIZE = 2 * 1024 * 1024;  // 2MB最大NALU大小
  static const size_t MAX_FU_A_FRAGMENTS = 1500;        // 最大FU-A片段数量
  size_t fuAFragmentCount_;       // 当前FU-A片段计数
};

PACK_ON
struct RtpHeader {
  struct {
    uint8_t cc : 4;
    uint8_t ext : 1;
    uint8_t padding : 1;
    uint8_t version : 2;
  } first_byte;
  uint8_t type;

  uint16_t seq;
  uint32_t timestamp;
  uint32_t ssrc;
} PACK_OFF;
typedef int (*onGotRtp_callback_t)(uint8_t *rtp, int rtpLen, uint32_t timestamp, void *userdata, void *context);
// rtp h264 打包
// 输入为 nalu 负载，输出为 rtp(回调)
class RtpH264Packer {
public:
  RtpH264Packer(onGotRtp_callback_t callback, void *userdata, void *context);
  enum {
    emNALU_SLICE,
    emNALU_START,
    emNALU_END,
    emNALU_SINGLE,
  };

public:
  int enqueueNaluPayload(NALU_t *NaluFragment);

private:
  int processSTAP_A(uint8_t *rtpPayload, int len);

  int processFU_A(uint8_t *rtpPayload, int len);

  int processRtpHeader(uint8_t *rtpPayload, int len);

  int onPackSingleNaluRtp(uint8_t *naluPayload, int len, int startOrEnd, int NRI);

private:
  onGotRtp_callback_t  onGotRtp_func_;
  uint32_t             timestamp_ = time(NULL);
  void                *userdata_;
  std::vector<uint8_t> RtpBuff_;  // 存放一个 nulu 的 buff;
  FU_IDENTIFIER        fu_ident;
  FU_HEADER            fu_header;
  void                *context_;
  // FILE* pH264File_;
};

class NaluH264Pack {
public:
  NaluH264Pack(onGotRtp_callback_t onGotRtp_func_, void *context);

  int onPackNaluSlice(NALU_t *naluFragement) {
    if (naluFragement == nullptr) {
      return -1;
    }
    rtp_packer.enqueueNaluPayload(naluFragement);
    return 0;
  }

private:
  RtpH264Packer rtp_packer;
  RtpHeader     header_;
  void         *context_;  // 上下文保存器
};

#endif /* RTP_H264_UNPACKER_H */
