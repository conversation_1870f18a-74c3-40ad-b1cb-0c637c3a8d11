#include "plugin_loader.h"
#include <iostream>
#include <cstring>
#include <cstring>

PluginLoader::PluginLoader() 
    : plugin_handle_(nullptr), loaded_(false) {
    memset(&interface_, 0, sizeof(interface_));
}

PluginLoader::~PluginLoader() {
    unload_plugin();
}

int PluginLoader::load_plugin(const std::string& plugin_path) {
    // 如果已经加载了插件，先卸载
    if (loaded_) {
        unload_plugin();
    }
    
    // 使用dlopen加载动态库
    plugin_handle_ = dlopen(plugin_path.c_str(), RTLD_LAZY);
    if (!plugin_handle_) {
        std::cerr << "Failed to load plugin: " << dlerror() << std::endl;
        return -1;
    }
    
    // 加载符号
    if (load_symbols() != 0) {
        std::cerr << "Failed to load plugin symbols" << std::endl;
        cleanup();
        return -1;
    }
    
    plugin_path_ = plugin_path;
    loaded_ = true;
    
    std::cout << "Plugin loaded successfully: " << plugin_path << std::endl;
    return 0;
}

void PluginLoader::unload_plugin() {
    if (plugin_handle_) {
        dlclose(plugin_handle_);
        plugin_handle_ = nullptr;
    }
    
    cleanup();
}

bool PluginLoader::is_loaded() const {
    return loaded_;
}

const struct plugin_interface* PluginLoader::get_interface() const {
    if (!loaded_) {
        return nullptr;
    }
    return &interface_;
}

const std::string& PluginLoader::get_plugin_path() const {
    return plugin_path_;
}

uint8_t PluginLoader::get_plugin_version() const {
    // 尝试从插件中获取版本信息
    // 这里可以定义一个可选的版本获取函数
    typedef uint8_t (*get_plugin_version_func_t)();
    get_plugin_version_func_t get_version = 
        (get_plugin_version_func_t)dlsym(plugin_handle_, "get_plugin_version");
    
    if (get_version) {
        return get_version();
    }
    
    // 如果插件没有提供版本函数，返回默认版本
    return 1; // 默认版本1.0
}

int PluginLoader::load_symbols() {
    if (!plugin_handle_) {
        return -1;
    }
    
    // 清除之前的错误
    dlerror();
    
    // 加载核心接口
    interface_.ipc_create_config = (ipc_create_config_func_t)dlsym(plugin_handle_, "ipc_create_config");
    if (!interface_.ipc_create_config) {
        std::cerr << "Failed to load symbol: ipc_create_config - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_create_decoder = (ipc_create_decoder_func_t)dlsym(plugin_handle_, "ipc_create_decoder");
    if (!interface_.ipc_create_decoder) {
        std::cerr << "Failed to load symbol: ipc_create_decoder - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_destroy_decoder = (ipc_destroy_decoder_func_t)dlsym(plugin_handle_, "ipc_destroy_decoder");
    if (!interface_.ipc_destroy_decoder) {
        std::cerr << "Failed to load symbol: ipc_destroy_decoder - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_process_packet = (ipc_process_packet_func_t)dlsym(plugin_handle_, "ipc_process_packet");
    if (!interface_.ipc_process_packet) {
        std::cerr << "Failed to load symbol: ipc_process_packet - " << dlerror() << std::endl;
        return -1;
    }
    
    // 加载配置接口
    interface_.ipc_cfg_set_user_data = (ipc_cfg_set_user_data_func_t)dlsym(plugin_handle_, "ipc_cfg_set_user_data");
    if (!interface_.ipc_cfg_set_user_data) {
        std::cerr << "Failed to load symbol: ipc_cfg_set_user_data - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_cfg_set_config_flag = (ipc_cfg_set_config_flag_func_t)dlsym(plugin_handle_, "ipc_cfg_set_config_flag");
    if (!interface_.ipc_cfg_set_config_flag) {
        std::cerr << "Failed to load symbol: ipc_cfg_set_config_flag - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_cfg_set_config_frame_type = (ipc_cfg_set_config_frame_type_func_t)dlsym(plugin_handle_, "ipc_cfg_set_config_frame_type");
    if (!interface_.ipc_cfg_set_config_frame_type) {
        std::cerr << "Failed to load symbol: ipc_cfg_set_config_frame_type - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_cfg_set_config_log_level = (ipc_cfg_set_config_log_level_func_t)dlsym(plugin_handle_, "ipc_cfg_set_config_log_level");
    if (!interface_.ipc_cfg_set_config_log_level) {
        std::cerr << "Failed to load symbol: ipc_cfg_set_config_log_level - " << dlerror() << std::endl;
        return -1;
    }

    interface_.ipc_cfg_set_exclude_packet_ip = (ipc_cfg_set_exclude_packet_ip_func_t)dlsym(plugin_handle_, "ipc_cfg_set_exclude_packet_ip");
    if (!interface_.ipc_cfg_set_exclude_packet_ip) {
        std::cerr << "Failed to load symbol: ipc_cfg_set_exclude_packet_ip - " << dlerror() << std::endl;
        return -1;
    }
    
    // 加载查询接口
    interface_.ipc_deocder_get_stream_capability = (ipc_deocder_get_stream_capability_func_t)dlsym(plugin_handle_, "ipc_deocder_get_stream_capability");
    if (!interface_.ipc_deocder_get_stream_capability) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_stream_capability - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_deocder_get_stream_latest_sps = (ipc_deocder_get_stream_latest_sps_func_t)dlsym(plugin_handle_, "ipc_deocder_get_stream_latest_sps");
    if (!interface_.ipc_deocder_get_stream_latest_sps) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_stream_latest_sps - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_deocder_get_stream_latest_pps = (ipc_deocder_get_stream_latest_pps_func_t)dlsym(plugin_handle_, "ipc_deocder_get_stream_latest_pps");
    if (!interface_.ipc_deocder_get_stream_latest_pps) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_stream_latest_pps - " << dlerror() << std::endl;
        return -1;
    }
    
    interface_.ipc_deocder_get_deviceid = (ipc_deocder_get_deviceid_func_t)dlsym(plugin_handle_, "ipc_deocder_get_deviceid");
    if (!interface_.ipc_deocder_get_deviceid) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_deviceid - " << dlerror() << std::endl;
        return -1;
    }

    interface_.ipc_deocder_get_advanced_video_coding = (ipc_deocder_get_advanced_video_coding_func_t)dlsym(plugin_handle_, "ipc_deocder_get_advanced_video_coding");
    if (!interface_.ipc_deocder_get_advanced_video_coding) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_advanced_video_coding - " << dlerror() << std::endl;
        return -1;
    }

    interface_.ipc_deocder_get_advanced_audio_coding = (ipc_deocder_get_advanced_audio_coding_func_t)dlsym(plugin_handle_, "ipc_deocder_get_advanced_audio_coding");
    if (!interface_.ipc_deocder_get_advanced_audio_coding) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_advanced_audio_coding - " << dlerror() << std::endl;
        return -1;
    }

    interface_.ipc_deocder_get_ipc_protocol = (ipc_deocder_get_ipc_protocol_func_t)dlsym(plugin_handle_, "ipc_deocder_get_ipc_protocol");
    if (!interface_.ipc_deocder_get_ipc_protocol) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_ipc_protocol - " << dlerror() << std::endl;
        return -1;
    }

    interface_.ipc_deocder_get_stream_sample_rate = (ipc_deocder_get_stream_sample_rate_func_t)dlsym(plugin_handle_, "ipc_deocder_get_stream_sample_rate");
    if (!interface_.ipc_deocder_get_stream_sample_rate) {
        std::cerr << "Failed to load symbol: ipc_deocder_get_stream_sample_rate - " << dlerror() << std::endl;
        return -1;
    }

    std::cout << "Plugin loaded successfully: " << plugin_path_ << std::endl;
    return 0;
}

void PluginLoader::cleanup() {
    memset(&interface_, 0, sizeof(interface_));
    plugin_path_.clear();
    loaded_ = false;
}
