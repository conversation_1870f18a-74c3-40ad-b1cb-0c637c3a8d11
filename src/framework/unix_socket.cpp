#include "unix_socket.h"
#include <iostream>
#include <cstring>
#include <sys/epoll.h>
#include <errno.h>
#include <fcntl.h>
#include <mutex>
#include <algorithm>
#include <vector>


// UnixSocketServer 基类实现
UnixSocketServer::UnixSocketServer(const std::string& socket_path, SocketType type)
    : socket_path_(socket_path), server_fd_(-1), running_(false),
      socket_type_(type), sequence_counter_(0) {
}

UnixSocketServer::~UnixSocketServer() {
    stop();
}

bool UnixSocketServer::start() {
    if (running_.load()) {
        return true;
    }
    
    if (!setup_server_socket()) {
        return false;
    }
    
    // DGRAM套接字不需要epoll设置
    
    running_.store(true);

    // DGRAM套接字只需要消息处理线程
    process_thread_ = std::thread(&UnixSocketServer::process_loop, this);
    
    std::cout << "Unix Socket Server started on: " << socket_path_ << std::endl;
    return true;
}

void UnixSocketServer::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);

    // 等待消息处理线程结束
    if (process_thread_.joinable()) {
        process_thread_.join();
    }
    
    cleanup();
    std::cout << "Unix Socket Server stopped" << std::endl;
}

void UnixSocketServer::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

// ============================================================================
// UnixSocketServerDgram 实现（DGRAM套接字，用于NALU传输）
// ============================================================================

UnixSocketServerDgram::UnixSocketServerDgram(const std::string& socket_path)
    : UnixSocketServer(socket_path, SocketType::DGRAM) {
    memset(&current_client_addr_, 0, sizeof(current_client_addr_));
    current_client_len_ = 0;
}

UnixSocketServerDgram::~UnixSocketServerDgram() {
}

bool UnixSocketServerDgram::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(client_fd, header, data);
}

void UnixSocketServerDgram::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    // DGRAM套接字的广播：发送到最后一个已知的客户端地址
    // 如果没有客户端地址，则忽略广播
    if (current_client_len_ > 0) {
        std::cout << "Broadcasting DGRAM message type " << msg_type << " to client, length=" << length << std::endl;
        ipc_msg_hdr header;
        header.proto_version = 10;  // v1.0
        header.msg_type = msg_type;
        header.msg_len = length;
        header.transaction_id = sequence_counter_.fetch_add(1);

        // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
        size_t total_size = sizeof(header) + length;
        std::vector<uint8_t> message_buffer(total_size);

        // 拷贝消息头
        memcpy(message_buffer.data(), &header, sizeof(header));

        // 拷贝消息体（如果有）
        if (length > 0 && data) {
            memcpy(message_buffer.data() + sizeof(header), data, length);
        }

        // 一次性发送完整消息
        ssize_t sent = sendto(server_fd_, message_buffer.data(), total_size, 0,
                             (struct sockaddr*)&current_client_addr_, current_client_len_);
        if (sent == -1) {
            std::cerr << "Failed to send DGRAM message: " << strerror(errno) << std::endl;
        } else if (sent != static_cast<ssize_t>(total_size)) {
            std::cerr << "Partial DGRAM message sent: " << sent << "/" << total_size << " bytes" << std::endl;
        } else {
            std::cout << "Successfully sent DGRAM message: " << sent << " bytes" << std::endl;
        }
    } else {
        std::cout << "No client address recorded, cannot broadcast DGRAM message type " << msg_type << std::endl;
    }
}

size_t UnixSocketServerDgram::get_client_count() const {
    // DGRAM套接字没有持久连接，返回是否有已知客户端地址
    return (current_client_len_ > 0) ? 1 : 0;
}

bool UnixSocketServerDgram::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());

    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (server_fd_ == -1) {
        std::cerr << "Failed to create socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        std::cerr << "Failed to bind socket: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    return true;
}

void UnixSocketServerDgram::cleanup() {
    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 删除socket文件
    unlink(socket_path_.c_str());
}

bool UnixSocketServerDgram::send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data) {
    // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
    size_t total_size = sizeof(header) + header.msg_len;
    std::vector<uint8_t> message_buffer(total_size);

    // 拷贝消息头
    memcpy(message_buffer.data(), &header, sizeof(header));

    // 拷贝消息体（如果有）
    if (header.msg_len > 0 && data) {
        memcpy(message_buffer.data() + sizeof(header), data, header.msg_len);
    }

    // 一次性发送完整消息到最后已知的客户端地址
    if (current_client_len_ > 0) {
        ssize_t sent = sendto(server_fd_, message_buffer.data(), total_size, 0,
                             (struct sockaddr*)&current_client_addr_, current_client_len_);
        return sent == static_cast<ssize_t>(total_size);
    }

    return false;
}

void UnixSocketServerDgram::process_loop() {
    uint8_t buffer[MAX_MESSAGE_SIZE];
    struct sockaddr_un client_addr;
    socklen_t client_len;

    while (running_.load()) {
        client_len = sizeof(client_addr);

        // 使用recvfrom接收数据报，设置超时
        struct timeval timeout;
        timeout.tv_sec = 1;  // 1秒超时
        timeout.tv_usec = 0;

        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(server_fd_, &readfds);

        int result = select(server_fd_ + 1, &readfds, NULL, NULL, &timeout);
        if (result == -1) {
            if (errno != EINTR && running_.load()) {
                std::cerr << "select failed: " << strerror(errno) << std::endl;
            }
            continue;
        } else if (result == 0) {
            // 超时，继续循环
            continue;
        }

        if (FD_ISSET(server_fd_, &readfds)) {
            ssize_t bytes_read = recvfrom(server_fd_, buffer, sizeof(buffer), 0,
                                        (struct sockaddr*)&client_addr, &client_len);

            if (bytes_read > 0) {
                // 解析消息，使用server_fd_作为虚拟客户端ID
                parse_message(server_fd_, buffer, bytes_read);

                // 记录客户端地址用于回复
                current_client_addr_ = client_addr;
                current_client_len_ = client_len;
            } else if (bytes_read == -1 && errno != EAGAIN && errno != EWOULDBLOCK) {
                std::cerr << "recvfrom failed: " << strerror(errno) << std::endl;
            }
        }
    }
}

// DGRAM套接字不需要单独的receive_message函数

bool UnixSocketServer::parse_message(int client_fd, const uint8_t* buffer, size_t length) {
    if (length < sizeof(ipc_msg_hdr)) {
        std::cerr << "Message too short" << std::endl;
        return false;
    }

    const ipc_msg_hdr* header = reinterpret_cast<const ipc_msg_hdr*>(buffer);

    if (header->proto_version != 10) {
        std::cerr << "Invalid protocol version" << std::endl;
        return false;
    }

    if (header->msg_len > MAX_MESSAGE_SIZE - sizeof(ipc_msg_hdr)) {
        std::cerr << "Message too large" << std::endl;
        return false;
    }

    if (length < sizeof(ipc_msg_hdr) + header->msg_len) {
        std::cerr << "Incomplete message" << std::endl;
        return false;
    }

    const uint8_t* data = buffer + sizeof(ipc_msg_hdr);

    if (message_handler_) {
        message_handler_(header->msg_type, data, header->msg_len, client_fd);
    }

    return true;
}

// ============================================================================
// UnixSocketServerStream 实现（STREAM套接字，用于控制消息）
// ============================================================================

UnixSocketServerStream::UnixSocketServerStream(const std::string& socket_path)
    : UnixSocketServer(socket_path, SocketType::STREAM), epoll_fd_(-1) {
}

UnixSocketServerStream::~UnixSocketServerStream() {
}

bool UnixSocketServerStream::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(client_fd, header, data);
}

void UnixSocketServerStream::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    std::lock_guard<std::mutex> lock(clients_mutex_);
    for (int client_fd : client_fds_) {
        send_message_internal(client_fd, header, data);
    }
}

size_t UnixSocketServerStream::get_client_count() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    return client_fds_.size();
}

bool UnixSocketServerStream::send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体
    if (header.msg_len > 0 && data) {
        sent = send(client_fd, data, header.msg_len, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketServerStream::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());

    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (server_fd_ == -1) {
        std::cerr << "Failed to create socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        std::cerr << "Failed to bind socket: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 监听连接
    if (listen(server_fd_, 10) == -1) {
        std::cerr << "Failed to listen: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 创建epoll
    epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
    if (epoll_fd_ == -1) {
        std::cerr << "Failed to create epoll: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }

    // 添加服务器socket到epoll
    struct epoll_event ev;
    ev.events = EPOLLIN;
    ev.data.fd = server_fd_;
    if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, server_fd_, &ev) == -1) {
        std::cerr << "Failed to add server socket to epoll: " << strerror(errno) << std::endl;
        close(epoll_fd_);
        close(server_fd_);
        epoll_fd_ = -1;
        server_fd_ = -1;
        return false;
    }

    return true;
}

void UnixSocketServerStream::process_loop() {
    // 启动accept线程
    accept_thread_ = std::thread(&UnixSocketServerStream::accept_loop, this);

    const int MAX_EVENTS = 10;
    struct epoll_event events[MAX_EVENTS];

    while (running_.load()) {
        int nfds = epoll_wait(epoll_fd_, events, MAX_EVENTS, 1000);
        if (nfds == -1) {
            if (errno != EINTR && running_.load()) {
                std::cerr << "epoll_wait failed: " << strerror(errno) << std::endl;
            }
            continue;
        }

        for (int i = 0; i < nfds; i++) {
            int fd = events[i].data.fd;
            if (fd != server_fd_) {
                // 客户端数据
                if (events[i].events & (EPOLLIN | EPOLLHUP | EPOLLERR)) {
                    if (!receive_message(fd)) {
                        remove_client(fd);
                    }
                }
            }
        }
    }

    // 等待accept线程结束
    if (accept_thread_.joinable()) {
        accept_thread_.join();
    }
}

void UnixSocketServerStream::cleanup() {
    // 关闭所有客户端连接
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        for (int client_fd : client_fds_) {
            close(client_fd);
        }
        client_fds_.clear();
    }

    // 关闭epoll
    if (epoll_fd_ != -1) {
        close(epoll_fd_);
        epoll_fd_ = -1;
    }

    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }

    // 删除socket文件
    unlink(socket_path_.c_str());
}

void UnixSocketServerStream::accept_loop() {
    while (running_.load()) {
        struct sockaddr_un client_addr;
        socklen_t client_len = sizeof(client_addr);

        int client_fd = accept(server_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd == -1) {
            if (errno != EINTR && running_.load()) {
                std::cerr << "accept failed: " << strerror(errno) << std::endl;
            }
            continue;
        }

        add_client(client_fd);
    }
}

void UnixSocketServerStream::add_client(int client_fd) {
    // 添加到客户端列表
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        client_fds_.push_back(client_fd);
    }

    // 添加到epoll
    struct epoll_event ev;
    ev.events = EPOLLIN | EPOLLHUP | EPOLLERR;
    ev.data.fd = client_fd;
    if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, client_fd, &ev) == -1) {
        std::cerr << "Failed to add client to epoll: " << strerror(errno) << std::endl;
        remove_client(client_fd);
    }
}

void UnixSocketServerStream::remove_client(int client_fd) {
    // 从epoll移除
    epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, client_fd, nullptr);

    // 关闭socket
    close(client_fd);

    // 从客户端列表移除
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        client_fds_.erase(std::remove(client_fds_.begin(), client_fds_.end(), client_fd), client_fds_.end());
    }
}

bool UnixSocketServerStream::receive_message(int client_fd) {
    uint8_t buffer[MAX_MESSAGE_SIZE];
    ssize_t received = recv(client_fd, buffer, sizeof(buffer), 0);

    if (received <= 0) {
        return false;
    }

    return parse_message(client_fd, buffer, received);
}

// ============================================================================
// UnixSocketClient 基类实现
// ============================================================================

UnixSocketClient::UnixSocketClient(const std::string& socket_path, SocketType type)
    : socket_path_(socket_path), client_fd_(-1), connected_(false),
      receiving_(false), socket_type_(type), sequence_counter_(0) {
}

UnixSocketClient::~UnixSocketClient() {
    disconnect();
}



void UnixSocketClient::disconnect() {
    if (!connected_.load()) {
        return;
    }

    stop_async_receive();

    connected_.store(false);

    if (client_fd_ != -1) {
        close(client_fd_);
        client_fd_ = -1;
    }

    std::cout << "Disconnected from Unix Socket Server" << std::endl;
}

void UnixSocketClient::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

void UnixSocketClient::start_async_receive() {
    if (receiving_.load() || !connected_.load()) {
        return;
    }

    receiving_.store(true);
    receive_thread_ = std::thread(&UnixSocketClient::receive_loop, this);
}

void UnixSocketClient::stop_async_receive() {
    if (!receiving_.load()) {
        return;
    }

    receiving_.store(false);

    if (receive_thread_.joinable()) {
        receive_thread_.join();
    }
}

// ============================================================================
// UnixSocketClientDgram 实现（DGRAM套接字，用于NALU接收）
// ============================================================================

UnixSocketClientDgram::UnixSocketClientDgram(const std::string& socket_path)
    : UnixSocketClient(socket_path, SocketType::DGRAM) {
    memset(&server_addr_, 0, sizeof(server_addr_));
}

UnixSocketClientDgram::~UnixSocketClientDgram() {
}

bool UnixSocketClientDgram::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_DGRAM, 0);
    if (client_fd_ == -1) {
        std::cerr << "Failed to create DGRAM client socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置服务器地址（DGRAM不需要connect，只需要保存地址）
    server_addr_.sun_family = AF_UNIX;
    strncpy(server_addr_.sun_path, socket_path_.c_str(), sizeof(server_addr_.sun_path) - 1);

    connected_.store(true);
    std::cout << "DGRAM client ready for server: " << socket_path_ << std::endl;
    return true;
}

bool UnixSocketClientDgram::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool UnixSocketClientDgram::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClientDgram::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClientDgram::send_message_internal(const ipc_msg_hdr& header, const uint8_t* data) {
    // DGRAM套接字需要将消息头和消息体拼接成一个完整的数据报发送
    size_t total_size = sizeof(header) + header.msg_len;
    std::vector<uint8_t> message_buffer(total_size);

    // 拷贝消息头
    memcpy(message_buffer.data(), &header, sizeof(header));

    // 拷贝消息体（如果有）
    if (header.msg_len > 0 && data) {
        memcpy(message_buffer.data() + sizeof(header), data, header.msg_len);
    }

    // 一次性发送完整消息
    ssize_t sent = sendto(client_fd_, message_buffer.data(), total_size, 0,
                         (struct sockaddr*)&server_addr_, sizeof(server_addr_));

    return sent == static_cast<ssize_t>(total_size);
}

bool UnixSocketClientDgram::receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data) {
    struct sockaddr_un from_addr;
    socklen_t from_len = sizeof(from_addr);

    // 接收完整消息（头部+数据）
    uint8_t buffer[MAX_MESSAGE_SIZE];
    ssize_t received = recvfrom(client_fd_, buffer, sizeof(buffer), 0,
                               (struct sockaddr*)&from_addr, &from_len);

    if (received < static_cast<ssize_t>(sizeof(header))) {
        return false;
    }

    // 解析消息头
    memcpy(&header, buffer, sizeof(header));

    if (header.proto_version != 10) {
        std::cerr << "Invalid protocol version in received message" << std::endl;
        return false;
    }

    if (header.msg_len > MAX_MESSAGE_SIZE - sizeof(header)) {
        std::cerr << "Message too large: " << header.msg_len << std::endl;
        return false;
    }

    // 验证接收到的数据长度
    if (received != static_cast<ssize_t>(sizeof(header) + header.msg_len)) {
        std::cerr << "Incomplete message received" << std::endl;
        return false;
    }

    // 解析消息体
    if (header.msg_len > 0) {
        data.resize(header.msg_len);
        memcpy(data.data(), buffer + sizeof(header), header.msg_len);
    } else {
        data.clear();
    }

    return true;
}

// ============================================================================
// UnixSocketClientStream 实现（STREAM套接字，用于控制消息）
// ============================================================================

UnixSocketClientStream::UnixSocketClientStream(const std::string& socket_path)
    : UnixSocketClient(socket_path, SocketType::STREAM) {
}

UnixSocketClientStream::~UnixSocketClientStream() {
}

bool UnixSocketClientStream::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (client_fd_ == -1) {
        std::cerr << "Failed to create STREAM client socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置服务器地址
    struct sockaddr_un server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sun_family = AF_UNIX;
    strncpy(server_addr.sun_path, socket_path_.c_str(), sizeof(server_addr.sun_path) - 1);

    // 连接到服务器
    if (::connect(client_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) == -1) {
        std::cerr << "Failed to connect to STREAM server: " << strerror(errno) << std::endl;
        close(client_fd_);
        client_fd_ = -1;
        return false;
    }

    connected_.store(true);
    std::cout << "STREAM client connected to server: " << socket_path_ << std::endl;
    return true;
}

bool UnixSocketClientStream::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    header.proto_version = 10;  // v1.0
    header.msg_type = msg_type;
    header.msg_len = length;
    header.transaction_id = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool UnixSocketClientStream::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    ipc_msg_hdr header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClientStream::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClientStream::send_message_internal(const ipc_msg_hdr& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd_, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体（如果有）
    if (header.msg_len > 0 && data) {
        sent = send(client_fd_, data, header.msg_len, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketClientStream::receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data) {
    // 接收消息头
    ssize_t received = recv(client_fd_, &header, sizeof(header), 0);
    if (received != sizeof(header)) {
        return false;
    }

    if (header.proto_version != 10) {
        std::cerr << "Invalid protocol version in received message" << std::endl;
        return false;
    }

    if (header.msg_len > MAX_MESSAGE_SIZE) {
        std::cerr << "Message too large: " << header.msg_len << std::endl;
        return false;
    }

    // 接收消息体
    if (header.msg_len > 0) {
        data.resize(header.msg_len);
        received = recv(client_fd_, data.data(), header.msg_len, 0);
        if (received != static_cast<ssize_t>(header.msg_len)) {
            return false;
        }
    } else {
        data.clear();
    }

    return true;
}
