#ifndef __UNIX_SOCKET_H__
#define __UNIX_SOCKET_H__

#include <string>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <cstdint>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include "ipc/ipc_msg.h"

// 消息处理回调函数类型
typedef std::function<void(uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd)> MessageHandler;

// 套接字类型枚举
enum class SocketType {
    STREAM,
    DGRAM
};

// 基础Unix Socket服务器类（抽象基类）
class UnixSocketServer {
public:
    UnixSocketServer(const std::string& socket_path, SocketType type);
    virtual ~UnixSocketServer();

    // 启动服务器
    bool start();

    // 停止服务器
    void stop();

    // 设置消息处理回调
    void set_message_handler(MessageHandler handler);

    // 发送消息到指定客户端（纯虚函数）
    virtual bool send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) = 0;

    // 广播消息到所有客户端（纯虚函数）
    virtual void broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) = 0;

    // 获取连接的客户端数量（纯虚函数）
    virtual size_t get_client_count() const = 0;

protected:
    // 子类需要实现的虚函数
    virtual bool setup_server_socket() = 0;
    virtual void process_loop() = 0;
    virtual void cleanup() = 0;

    // 子类可访问的成员
    std::string socket_path_;
    int server_fd_;
    std::atomic<bool> running_;
    std::thread process_thread_;
    MessageHandler message_handler_;
    SocketType socket_type_;

    // 序列号生成
    std::atomic<uint32_t> sequence_counter_;

    // 通用辅助方法
    bool parse_message(int client_fd, const uint8_t* buffer, size_t length);

    // 常量定义
    static const size_t MAX_MESSAGE_SIZE = 256 * 1024;
};

// DGRAM套接字服务器（用于NALU传输）
class UnixSocketServerDgram : public UnixSocketServer {
public:
    UnixSocketServerDgram(const std::string& socket_path);
    virtual ~UnixSocketServerDgram();

    // 实现基类的纯虚函数
    virtual bool send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual void broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual size_t get_client_count() const override;

protected:
    virtual bool setup_server_socket() override;
    virtual void process_loop() override;
    virtual void cleanup() override;

private:
    // DGRAM套接字客户端地址管理
    struct sockaddr_un current_client_addr_;
    socklen_t current_client_len_;

    bool send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data);
};

// STREAM套接字服务器（用于控制消息）
class UnixSocketServerStream : public UnixSocketServer {
public:
    UnixSocketServerStream(const std::string& socket_path);
    virtual ~UnixSocketServerStream();

    // 实现基类的纯虚函数
    virtual bool send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual void broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual size_t get_client_count() const override;

protected:
    virtual bool setup_server_socket() override;
    virtual void process_loop() override;
    virtual void cleanup() override;

private:
    // STREAM套接字客户端管理
    std::vector<int> client_fds_;
    mutable std::mutex clients_mutex_;
    int epoll_fd_;
    std::thread accept_thread_;

    void accept_loop();
    void add_client(int client_fd);
    void remove_client(int client_fd);
    bool receive_message(int client_fd);
    bool send_message_internal(int client_fd, const ipc_msg_hdr& header, const uint8_t* data);
};

// 基础Unix Socket客户端类（抽象基类）
class UnixSocketClient {
public:
    UnixSocketClient(const std::string& socket_path, SocketType type);
    virtual ~UnixSocketClient();

    // 连接到服务器（纯虚函数）
    virtual bool connect() = 0;

    // 断开连接
    void disconnect();

    // 发送消息（纯虚函数）
    virtual bool send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) = 0;

    // 接收消息（阻塞）（纯虚函数）
    virtual bool receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms = 0) = 0;

    // 设置消息处理回调（异步接收）
    void set_message_handler(MessageHandler handler);

    // 启动异步接收
    void start_async_receive();

    // 停止异步接收
    void stop_async_receive();

protected:
    std::string socket_path_;
    int client_fd_;
    std::atomic<bool> connected_;
    std::atomic<bool> receiving_;
    std::thread receive_thread_;
    MessageHandler message_handler_;
    SocketType socket_type_;

    // 序列号生成
    std::atomic<uint32_t> sequence_counter_;

    // 内部方法（纯虚函数）
    virtual void receive_loop() = 0;

    // 常量定义
    static const size_t MAX_MESSAGE_SIZE = 256 * 1024; // 优化：减少到256KB
};

// DGRAM套接字客户端（用于NALU接收）
class UnixSocketClientDgram : public UnixSocketClient {
public:
    UnixSocketClientDgram(const std::string& socket_path);
    virtual ~UnixSocketClientDgram();

    // 实现基类的纯虚函数
    virtual bool connect() override;
    virtual bool send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual bool receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms = 0) override;

    // 重写disconnect方法以清理客户端socket文件
    void disconnect();

protected:
    virtual void receive_loop() override;

private:
    // DGRAM套接字服务器地址
    struct sockaddr_un server_addr_;
    // 客户端socket路径
    std::string client_socket_path_;

    bool send_message_internal(const ipc_msg_hdr& header, const uint8_t* data);
    bool receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data);
};

// STREAM套接字客户端（用于控制消息）
class UnixSocketClientStream : public UnixSocketClient {
public:
    UnixSocketClientStream(const std::string& socket_path);
    virtual ~UnixSocketClientStream();

    // 实现基类的纯虚函数
    virtual bool connect() override;
    virtual bool send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) override;
    virtual bool receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms = 0) override;

protected:
    virtual void receive_loop() override;

private:
    bool send_message_internal(const ipc_msg_hdr& header, const uint8_t* data);
    bool receive_message_internal(ipc_msg_hdr& header, std::vector<uint8_t>& data);
};

#endif // __UNIX_SOCKET_H__
