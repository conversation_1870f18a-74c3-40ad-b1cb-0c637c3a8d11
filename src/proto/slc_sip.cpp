
#include "slc_sip.h"
#include "ipc/ipc_decoder.h"
#include "slc_common.h"
#include "slc_observer.h"
#include <cstring>

#include "string.h"
enum _via_header_idx {
  EM_TRANSPORT,
  EM_SENT_BY_ADDRESS,
  EM_SENT_BY_PORT,
  EM_RPORT,
  EM_BRANCH,
  EM_RECIEVED
};

enum _owner_body_idx {
  EM_OWNER_NAME,
  EM_SESSION_ID,
  EM_SESSION_VERSION,
  EM_OWNER_NETWORK_TYPE,
  EM_OWNER_ADDRESS_TYPE,
  EM_OWNER_ADDRESS,
};

enum _connec_body_idx {
  EM_CONNNECT_NETWORK_TYPE,
  EM_CONNNECT_ADDRESS_TYPE,
  EM_CONNNECT_ADDRESS
};

enum _time_descrip_idx {
  EM_SESSION_START_TIME,
  EM_SESSION_STOP_TIME
};

enum _media_descrip_idx {
  EM_MEDIA_TYPE,
  EM_MEDIA_PORT,
  EM_MEDIA_PROTOCOL,
  EM_MEDIA_FORMAT1,
  EM_MEDIA_FORMAT2
};

enum _media_rtpmap_idx {
  EM_ATTR_FILEDNAME_RTPMAP,
  EM_FORMAT_RTPMAP,
  EM_MIME_TYPE_RTPMAP,
  EM_SAMPLE_RATE_RTPMAP
};

enum _media_fmtp_idx {
  EM_ATTR_FILEDNAME_FMTP,
  EM_FORMAT_FMTP,
  EM_FORMAT_SPECIFIC_PARAM_FMTP,
};

enum _media_ptime_idx {
  EM_ATTR_FILEDNAME_PTIME,
  EM_ATTRIBUTE_VALUE_PTIME
};

/* invoking the Message_header parsing func of the array by the "name" which corresponding to the header parsing func */
typedef void (*parse_header_func)(struct header_value*, sip_header_gather*);
#define HEADER_PARSE_AR(header_name, parse_func) \
  { (header_name), (parse_func) }
typedef struct __header_parse_array {
  const char*       header_name;
  parse_header_func parse_header_by_name;
} header_parse_array;

/*
* @param : mem_ptr - the pointer of member in header/body.
* @param : mem_len - how long do you want to copy.
* @param : result - where do you want to copy to.
* @param : result_len - we need the length of the target place to caculate if we can copy all the data.
*/
static int set_member_result(const uint8_t* mem_ptr, uint16_t mem_len, char* result, const uint16_t result_len) {
  if (NULL == mem_ptr || mem_len <= 0 || NULL == result || result_len <= 0)
    return -1;

  if (mem_len > result_len - 1)
    mem_len = result_len - 1;

  strncpy(result, (const char*)mem_ptr, mem_len);

  result[mem_len] = '\0';

  return 0;
}

/*
* @param : sign_name - if message header or body value don't have a type name before '=' or even don't have a '=', we need give it a NULL;
*/
static int judg_member_result(const uint8_t* mem_ptr, const uint16_t mem_len, char* result, const uint16_t result_len,
    const char* sign_name /* NULL is permit */) {
  if (NULL == mem_ptr || NULL == result)
    return -1;

  int ret;
  int offset = 0;
  int equal = 0;

  const uint8_t* begin = mem_ptr;
  const uint16_t total_len = mem_len;

  char compare_name[64];
  memset(compare_name, 0, sizeof(compare_name));

  equal = find_special_char(begin, total_len, '=');
  if (equal < 0) {
    ret = set_member_result(begin, total_len, result, result_len);
    if (ret != 0)
      return -1;
  } else {
    if (NULL == sign_name) {
      return -1;
    }

    ret = set_member_result(begin + offset, equal, compare_name, sizeof(compare_name));
    if (ret != 0)
      return -1;

    if (strcmp(sign_name, compare_name) != 0)
      return -1;

    offset += equal + 1;

    ret = set_member_result(begin + offset, total_len - offset, result, result_len);
    if (ret != 0)
      return -1;
  }
  return 0;
}

static int parse_via_first_part(content_value* first_part, sip_header_gather* header_gather, int* sign_num) {
  if (NULL == first_part || NULL == header_gather)
    return -1;

  int ret = 0;
  int offset = 0;
  int colon = 0;
  int sprit = 0;
  int blank = 0;
  int semicolon = 0;

  const uint8_t* begin = first_part->ptr;
  const uint16_t total_len = first_part->len;

  sip_via_header* via = &(header_gather->via);

  if (strncasecmp((const char*)begin, "SIP/", 4) == 0) {
    // first '/'
    sprit = find_special_char(begin + offset, total_len - offset, '/');
    if (sprit < 0)
      return -1;

    offset += sprit + 1;

    // second '/'
    sprit = find_special_char(begin + offset, total_len - offset, '/');
    if (sprit < 0)
      return -1;

    offset += sprit + 1;

    blank = find_blank_space(begin + offset, total_len - offset);
    if (blank < 0)
      return -1;

    ret = set_member_result(begin + offset, blank, via->transport, sizeof(via->transport));
    if (ret != 0)
      return -1;

    (*sign_num)++;  // jump the "EM_TRANSPORT"

    offset += blank + 1;

    colon = find_special_char(begin + offset, total_len - offset, ':');
    if (colon < 0)
      return -1;

    ret = set_member_result(begin + offset, colon, via->sent_addr, sizeof(via->sent_addr));
    if (ret != 0)
      return -1;

    (*sign_num)++;  // jump the "EM_SENT_BY_ADDRESS"

    offset += colon + 1;

    semicolon = find_special_char(begin + offset, total_len - offset, ';');
    if (semicolon < 0)
      return -1;

    ret = set_member_result(begin + offset, semicolon, via->sent_port, sizeof(via->sent_port));
    if (ret != 0)
      return -1;

    (*sign_num)++;  // jump the "EM_SENT_BY_PORT"
  }
  return 0;
}

static int get_first_part(struct header_value* value, content_value* first_part /* in & out */, int* offset /* in & out */) {
  if (NULL == value || NULL == first_part)
    return -1;

  int semicolon = 0;
  int right_arrow = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  semicolon = find_special_char(begin + *offset, total_len - *offset, ';');
  if (semicolon < 0) {
    // if no semicolon ';'
    right_arrow = find_special_char(begin + *offset, total_len - *offset, '>');
    if (right_arrow < 0)
      return -1;

    first_part->ptr = begin + *offset;
    first_part->len = right_arrow - *offset + 1;  // invode the >
    *offset += right_arrow + 1;
    return right_arrow;
  } else {
    first_part->ptr = begin + *offset;
    first_part->len = semicolon - *offset + 1;  // invode the ;
    *offset += semicolon + 1;
    return semicolon;
  }
}

static void set_via_member(const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, sip_via_header* via) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == via)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_RPORT:
      judg_member_result(begin + *offset, mem_len, via->rport, sizeof(via->rport), "rport");
      break;
    case EM_BRANCH:
      judg_member_result(begin + *offset, mem_len, via->branch, sizeof(via->branch), "branch");
      break;
    case EM_RECIEVED:
      judg_member_result(begin + *offset, mem_len, via->recieved, sizeof(via->recieved), "recieved");
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static void parse_via_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int sign_num = 0;
  int semicolon = 0;
  int offset = 0;

  content_value first_part;
  memset(&first_part, 0, sizeof(first_part));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->via_line, (const char*)value->ptr, value->len);
  sip_via_header* via = &(header_gather->via);

  if (offset >= total_len)
    return;

  semicolon = get_first_part(value, &first_part, &offset);
  if (semicolon < 0)
    return;

  ret = parse_via_first_part(&first_part, header_gather, &sign_num);
  if (ret != 0) {
    return;
  }

  while (offset < total_len) {
    semicolon = find_special_char(begin + offset, total_len - offset, ';');
    if (semicolon < 0) {
      if (total_len - offset > 0) {
        set_via_member(begin, &offset, total_len - offset, &sign_num, via);
        return;
      }
    }
    set_via_member(begin, &offset, semicolon, &sign_num, via);
  }

  return;
}

static stand_address* get_standard_addr(sip_header_gather* header_gather, const char* header_name) {
  if (NULL == header_gather || NULL == header_name)
    return NULL;

  stand_address* addr = NULL;

  if (strcmp("from", header_name) == 0) {
    sip_from_header* from = &(header_gather->from);
    addr = &(from->addr);
  } else if (strcmp("to", header_name) == 0) {
    sip_to_header* to = &(header_gather->to);
    addr = &(to->addr);
  } else if (strcmp("contact", header_name) == 0) {
    sip_contact_header* contact = &(header_gather->contact);
    addr = &(contact->addr);
  }

  return addr;
}

/* format as XXXX@YYYY:ZZZZ */
static int parse_standard_part(content_value* first_part, sip_header_gather* header_gather, const char* header_name) {
  if (NULL == first_part || NULL == header_gather || NULL == header_name)
    return -1;

  int ret = 0;
  int offset = 0;

  int colon = 0;
  int at = 0;
  int right_arrow = 0;

  const uint8_t* begin = first_part->ptr;
  const uint16_t total_len = first_part->len;

  stand_address* addr = NULL;

  addr = get_standard_addr(header_gather, header_name);
  if (NULL == addr)
    return -1;

  // first ':';
  colon = find_special_char(begin + offset, total_len - offset, ':');
  if (colon < 0)
    return -1;

  offset += colon + 1;

  // find '@'
  at = find_special_char(begin + offset, total_len - offset, '@');
  if (at < 0)
    return -1;

  char if_plus = *(const char*)(begin + offset);
  if (if_plus == '+') {
    // get country code
    if (get_uint16_t(begin + offset + 1, 0) == ntohs(0x3836))  // 86 = "china"
      strcpy(addr->country_code, "china");

    // get e164_num data;
    ret = set_member_result(begin + offset + 1, at - 1, addr->e164_num, sizeof(addr->e164_num));
    if (ret != 0)
      return -1;
  }

  // get user part data;
  ret = set_member_result(begin + offset, at, addr->user_part, sizeof(addr->user_part));
  if (ret != 0)
    return -1;

  offset += at + 1;

  // second ':'
  colon = find_special_char(begin + offset, total_len - offset, ':');
  if (colon < total_len && colon >= 0) {
    // get the host part data;
    ret = set_member_result(begin + offset, colon, addr->host_part, sizeof(addr->host_part));
    if (ret != 0)
      return -1;

    offset += colon + 1;

    // find '>'
    right_arrow = find_special_char(begin + offset, total_len - offset, '>');
    if (right_arrow < 0)
      return -1;

    // get the host port data;
    ret = set_member_result(begin + offset, right_arrow, addr->host_port, sizeof(addr->host_port));
    if (ret != 0)
      return -1;
  } else {
    // find '>'
    right_arrow = find_special_char(begin + offset, total_len - offset, '>');
    if (right_arrow < 0)
      return -1;

    // get the host part data, no port data in here ;
    ret = set_member_result(begin + offset, right_arrow, addr->host_part, sizeof(addr->host_part));
    if (ret != 0)
      return -1;
  }
  return 0;
}

static void parse_from_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int offset = 0;
  int semicolon = 0;

  content_value first_part;
  memset(&first_part, 0, sizeof(first_part));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->from_line, (const char*)value->ptr, value->len);

  sip_from_header* from = &(header_gather->from);

  if (offset >= total_len)
    return;

  semicolon = get_first_part(value, &first_part, &offset);
  if (semicolon < 0)
    return;

  /* split the header value into two parts, here deal the first part: */
  ret = parse_standard_part(&first_part, header_gather, "from");
  if (ret != 0) {
    return;
  }

  // get the from tag data;
  ret = judg_member_result(begin + offset, total_len - offset, from->from_tag, sizeof(from->from_tag), "tag");
  if (ret != 0)
    return;

  return;
}

static void parse_to_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int offset = 0;
  int semicolon = 0;

  content_value first_part;
  memset(&first_part, 0, sizeof(first_part));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->to_line, (const char*)value->ptr, value->len);

  sip_to_header* to = &(header_gather->to);

  if (offset >= total_len)
    return;

  semicolon = get_first_part(value, &first_part, &offset);
  if (semicolon < 0)
    return;

  /* split the header value into two parts */
  ret = parse_standard_part(&first_part, header_gather, "to");
  if (ret != 0) {
    return;
  }

  // get the from tag data;
  ret = judg_member_result(begin + offset, total_len - offset, to->to_tag, sizeof(to->to_tag), "tag");
  if (ret != 0)
    return;

  return;
}

static void parse_cseq_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int blank = 0;
  int offset = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->cseq_line, (const char*)value->ptr, value->len);

  sip_cseq_header* cseq = &(header_gather->cseq);

  if (offset >= total_len)
    return;

  // may be there's no blank at first char;
  blank = find_blank_space(begin + offset, total_len - offset);
  if (blank < 0)
    return;

  // get the sequence_num;
  ret = set_member_result(begin + offset, blank, cseq->sequence_num, sizeof(cseq->sequence_num));
  if (ret != 0)
    return;

  offset += blank + 1;

  // get the method
  ret = set_member_result(begin + offset, total_len - offset, cseq->method, sizeof(cseq->method));
  if (ret != 0)
    return;

  return;
}

static void parse_contact_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int offset = 0;
  int semicolon = 0;

  content_value first_part;
  memset(&first_part, 0, sizeof(first_part));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->contact_line, (const char*)value->ptr, value->len);

  sip_contact_header* contact = &(header_gather->contact);

  if (offset >= total_len)
    return;

  semicolon = get_first_part(value, &first_part, &offset);
  if (semicolon < 0)
    return;

  ret = parse_standard_part(&first_part, header_gather, "contact");
  if (ret != 0) {
    return;
  }

  // get the param;
  ret = judg_member_result(begin + offset, total_len - offset, contact->param, sizeof(contact->param), NULL);
  if (ret != 0)
    return;

  return;
}

static void parse_user_agent_header(struct header_value* value, sip_header_gather* header_gather) {
  if (NULL == value || NULL == header_gather)
    return;

  int ret = 0;
  int sign_num = 0;
  int semicolon = 0;
  int offset = 0;

  content_value first_part;
  memset(&first_part, 0, sizeof(first_part));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  strncpy(header_gather->user_agent, (const char*)value->ptr, value->len);
} /* register the parsing header function to array */
static header_parse_array _header_parse_array[] = {
    HEADER_PARSE_AR("via", parse_via_header),
    HEADER_PARSE_AR("from", parse_from_header),
    HEADER_PARSE_AR("to", parse_to_header),
    HEADER_PARSE_AR("cseq", parse_cseq_header),
    HEADER_PARSE_AR("contact", parse_contact_header),
    HEADER_PARSE_AR("user-agent", parse_user_agent_header),
};

/* invoking the Message_body parsing func of the array by the "case" which corresponding to the body parsing func */
typedef void (*parse_body_func)(body_value*, sip_body_gather*);
#define BODY_PARSE_AR(case_name, parse_func) \
  { (case_name), (parse_func) }
typedef struct __body_parse_array {
  const char*     case_type;
  parse_body_func parse_body_by_case;
} body_parse_array;

static void set_owner_member(const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, owner_session* owner) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == owner)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_OWNER_NAME:
      judg_member_result(begin + *offset, mem_len, owner->name, sizeof(owner->name), NULL);
      break;
    case EM_SESSION_ID:
      judg_member_result(begin + *offset, mem_len, owner->session_id, sizeof(owner->session_id), NULL);
      break;
    case EM_SESSION_VERSION:
      judg_member_result(begin + *offset, mem_len, owner->session_version, sizeof(owner->session_version), NULL);
      break;
    case EM_OWNER_NETWORK_TYPE:
      judg_member_result(begin + *offset, mem_len, owner->network_type, sizeof(owner->network_type), NULL);
      break;
    case EM_OWNER_ADDRESS_TYPE:
      judg_member_result(begin + *offset, mem_len, owner->adddress_type, sizeof(owner->adddress_type), NULL);
      break;
    case EM_OWNER_ADDRESS:
      judg_member_result(begin + *offset, mem_len, owner->address, sizeof(owner->address), NULL);
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static void parse_owner_body(body_value* value, sip_body_gather* body_gather) {
  if (NULL == value || NULL == body_gather)
    return;

  int blank = 0;
  int sign_num = 0;
  int offset = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  owner_session* owner = &(body_gather->owner);

  while (offset < total_len) {
    blank = find_blank_space(begin + offset, total_len - offset);
    if (blank < 0) {
      if (total_len - blank > 0) {
        // there is no blank in the end of value, but still has data;
        set_owner_member(begin, &offset, total_len - offset, &sign_num, owner);
        return;
      }
    }
    set_owner_member(begin, &offset, blank, &sign_num, owner);
  }
  return;
}

/* [bug 表示] 该函数会导致程序崩溃 */
static void set_connec_member(const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, connection* connec) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == connec)
    return;

  const uint8_t* begin = mem_ptr;
  int            type = *sign_num;
  switch (*sign_num) {
    case EM_CONNNECT_NETWORK_TYPE:
      judg_member_result(begin + *offset, mem_len, connec->networdk_type, sizeof(connec->networdk_type), NULL);
      break;
    case EM_CONNNECT_ADDRESS_TYPE:
      judg_member_result(begin + *offset, mem_len, connec->address_type, sizeof(connec->address_type), NULL);
      break;
    case EM_CONNNECT_ADDRESS:
      judg_member_result(begin + *offset, mem_len, connec->address, sizeof(connec->address), NULL);
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static void parse_connec_body(body_value* value, sip_body_gather* body_gather) {
  if (NULL == value || NULL == body_gather)
    return;

  int blank = 0;
  int offset = 0;
  int sign_num = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  connection* connec = &(body_gather->connec);

  while (offset < total_len) {
    blank = find_blank_space(begin + offset, total_len - offset);
    if (blank < 0) {
      if (total_len - blank > 0) {
        set_connec_member(begin, &offset, total_len - offset, &sign_num, connec);
        return;
      }
    }
    set_connec_member(begin, &offset, blank, &sign_num, connec);
  }
  return;
}

static void set_time_descrip_member(
    const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, time_descrip* time) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == time)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_SESSION_START_TIME:
      judg_member_result(begin + *offset, mem_len, time->start_time, sizeof(time->start_time), NULL);
      break;
    case EM_SESSION_STOP_TIME:
      judg_member_result(begin + *offset, mem_len, time->stop_time, sizeof(time->stop_time), NULL);
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static int calculate_time(time_descrip* time) {
  if (NULL == time)
    return -1;

  int start_time = 0;
  int stop_time = 0;
  int keep_time = 0;

  if (!strlen(time->start_time) || !strlen(time->stop_time))
    return -1;

  start_time = atoi(time->start_time);
  stop_time = atoi(time->stop_time);

  keep_time = stop_time - start_time;

  if (keep_time < 0)
    return -1;

  sprintf(time->keep_time, "%d", keep_time);

  return keep_time;
}

static void parse_time_descrip_body(body_value* value, sip_body_gather* body_gather) {
  if (NULL == value || NULL == body_gather)
    return;

  int blank = 0;
  int offset = 0;
  int sign_num = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  time_descrip* time = &(body_gather->time);

  while (offset < total_len) {
    blank = find_blank_space(begin + offset, total_len - offset);
    if (blank < 0) {
      if (total_len - offset > 0) {
        set_time_descrip_member(begin, &offset, total_len - offset, &sign_num, time);
        calculate_time(time);
        return;
      }
    }
    set_time_descrip_member(begin, &offset, blank, &sign_num, time);
    calculate_time(time);
  }
  return;
}

static void set_media_descrip_member(
    const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, media_descrip* m_descrip) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == m_descrip)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_MEDIA_TYPE:
      judg_member_result(begin + *offset, mem_len, m_descrip->media_type, sizeof(m_descrip->media_type), NULL);
      break;
    case EM_MEDIA_PORT:
      judg_member_result(begin + *offset, mem_len, m_descrip->media_port, sizeof(m_descrip->media_port), NULL);
      break;
    case EM_MEDIA_PROTOCOL:
      judg_member_result(begin + *offset, mem_len, m_descrip->media_protocol, sizeof(m_descrip->media_protocol), NULL);
      break;
    case EM_MEDIA_FORMAT1:
      judg_member_result(begin + *offset, mem_len, m_descrip->media_format1, sizeof(m_descrip->media_format1), NULL);
      break;
    case EM_MEDIA_FORMAT2:
      judg_member_result(begin + *offset, mem_len, m_descrip->media_format2, sizeof(m_descrip->media_format2), NULL);
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static void parse_media_descrip_body(body_value* value, sip_body_gather* body_gather) {
  if (NULL == value || NULL == body_gather)
    return;

  int blank = 0;
  int offset = 0;
  int sign_num = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  media_descrip* m_descrip = &(body_gather->m_descrip);

  while (offset < total_len) {
    blank = find_blank_space(begin + offset, total_len - offset);
    if (blank < 0) {
      if (total_len - offset > 0) {
        set_media_descrip_member(begin, &offset, total_len - offset, &sign_num, m_descrip);
        return;
      }
    }
    set_media_descrip_member(begin, &offset, blank, &sign_num, m_descrip);
  }
  return;
}

static void set_rtpmap_member(
    const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, media_attribute* p_attr) {
  if (mem_ptr == NULL || offset == NULL || sign_num == NULL || p_attr == NULL)
    return;
  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_ATTR_FILEDNAME_RTPMAP:
      judg_member_result(begin + *offset, mem_len, p_attr->attr_name, sizeof(p_attr->attr_name), NULL);
      break;
    case EM_FORMAT_RTPMAP:
      judg_member_result(begin + *offset, mem_len, p_attr->format, sizeof(p_attr->format), NULL);
      break;
    case EM_MIME_TYPE_RTPMAP:
      //printf("men_len:%d, offset:%d\n",mem_len,*offset);
      judg_member_result(begin + *offset, mem_len, p_attr->mime_type, sizeof(p_attr->mime_type), NULL);  // 会出现栈溢出
      break;
    case EM_SAMPLE_RATE_RTPMAP:
      judg_member_result(begin + *offset, mem_len, p_attr->sample_rate, sizeof(p_attr->sample_rate), NULL);  // 会出现栈溢出
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static int parse_rtpmap_attr(body_value* value, media_attribute* p_attr) {
  if (NULL == value || NULL == p_attr)
    return -1;

  int offset = 0;
  int colon = 0;
  int blank = 0;
  int sprit = 0;
  int sign_num = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  colon = find_special_char(begin + offset, total_len - offset, ':');
  if (colon < 0)
    return -1;

  set_rtpmap_member(begin, &offset, colon, &sign_num, p_attr);

  blank = find_blank_space(begin + offset, total_len - offset);
  if (blank < 0)
    return -1;

  set_rtpmap_member(begin, &offset, blank, &sign_num, p_attr);

  sprit = find_special_char(begin + offset, total_len - offset, '/');
  if (sprit < 0)
    return -1;

  set_rtpmap_member(begin, &offset, sprit, &sign_num, p_attr);

  set_rtpmap_member(begin, &offset, total_len - offset, &sign_num, p_attr);

  return 0;
}

static void set_fmtp_member(const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, media_attribute* p_attr) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == p_attr)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_ATTR_FILEDNAME_FMTP:
      judg_member_result(begin + *offset, mem_len, p_attr->attr_name, sizeof(p_attr->attr_name), NULL);
      break;
    case EM_FORMAT_FMTP:
      judg_member_result(begin + *offset, mem_len, p_attr->format, sizeof(p_attr->format), NULL);
      break;
    case EM_FORMAT_SPECIFIC_PARAM_FMTP:
      set_member_result(begin + *offset, mem_len, p_attr->specific_param, sizeof(p_attr->specific_param));
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static int parse_fmtp_attr(body_value* value, media_attribute* p_attr) {
  if (NULL == value || NULL == p_attr)
    return -1;

  int offset = 0;
  int sign_num = 0;
  int colon = 0;
  int blank = 0;

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  colon = find_special_char(begin + offset, total_len - offset, ':');
  if (colon < 0)
    return -1;

  set_fmtp_member(begin, &offset, colon, &sign_num, p_attr);

  blank = find_blank_space(begin + offset, total_len - offset);
  if (blank < 0)
    return -1;

  set_fmtp_member(begin, &offset, blank, &sign_num, p_attr);

  set_fmtp_member(begin, &offset, total_len - offset, &sign_num, p_attr);

  return 0;
}

static void set_ptime_member(
    const uint8_t* mem_ptr, int* offset, const uint16_t mem_len, int* sign_num, media_attribute* p_attr) {
  if (NULL == mem_ptr || NULL == offset || NULL == sign_num || NULL == p_attr)
    return;

  const uint8_t* begin = mem_ptr;

  switch (*sign_num) {
    case EM_ATTR_FILEDNAME_PTIME:
      judg_member_result(begin + *offset, mem_len, p_attr->attr_name, sizeof(p_attr->attr_name), NULL);
      break;
    case EM_ATTRIBUTE_VALUE_PTIME:
      judg_member_result(begin + *offset, mem_len, p_attr->attr_value, sizeof(p_attr->attr_value), NULL);
      break;
    default:
      break;
  }

  *offset += mem_len + 1;
  (*sign_num)++;

  return;
}

static int parse_ptime_attr(body_value* value, media_attribute* p_attr) {
  if (NULL == value || NULL == p_attr)
    return -1;

  int            colon = 0;
  int            offset = 0;
  int            sign_num = 0;
  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;

  colon = find_special_char(begin + offset, total_len - offset, ':');
  if (colon < 0)
    return -1;

  set_ptime_member(begin, &offset, colon, &sign_num, p_attr);

  set_ptime_member(begin, &offset, total_len - offset, &sign_num, p_attr);

  return 0;
}

static void parse_media_attr_body(body_value* value, sip_body_gather* body_gather) {
  if (NULL == value || NULL == body_gather)
    return;

  int  ret = 0;
  int  colon = 0;
  int  offset = 0;
  char compare_name[64];
  memset(compare_name, 0, sizeof(compare_name));

  const uint8_t* begin = value->ptr;
  const uint16_t total_len = value->len;
  //printf("total_len:%d\n",total_len);

  if (body_gather->attr_num > SIP_REPEAT_GROUP_NUM - 1)
    return;

  media_attribute* p_attr = &(body_gather->m_attr[body_gather->attr_num]);

  if (offset < total_len) {
    colon = find_special_char(begin + offset, total_len - offset, ':');
    if (colon < 0)
      return;

    ret = set_member_result(begin + offset, colon, compare_name, sizeof(compare_name));
    if (ret != 0)
      return;

    //printf("compare_name:%s\n",compare_name);
    if (strcmp("rtpmap", compare_name) == 0) {
      ret = parse_rtpmap_attr(value, p_attr);
      if (ret != 0) {
        return;
      }
      body_gather->attr_num++;
    } else if (strcmp("fmtp", compare_name) == 0) {
      ret = parse_fmtp_attr(value, p_attr);
      if (ret != 0) {
        return;
      }
      body_gather->attr_num++;
    } else if (strcmp("ptime", compare_name) == 0) {
      ret = parse_ptime_attr(value, p_attr);
      if (ret != 0) {
        return;
      }
      body_gather->attr_num++;
    }
  }
  return;
}

/* register the parsing body function to array */
static body_parse_array _body_parse_array[] = {
    BODY_PARSE_AR("o", parse_owner_body),
    BODY_PARSE_AR("c", parse_connec_body),
    BODY_PARSE_AR("t", parse_time_descrip_body),
    BODY_PARSE_AR("m", parse_media_descrip_body),
    BODY_PARSE_AR("a", parse_media_attr_body),
};

static void _free_header(gpointer data) {
  if (NULL == data)
    return;

  if (data != NULL) {
    free(data);
    data = NULL;
    return;
  }
}
static void _free_key_value(gpointer data) {
  if (NULL == data)
    return;

  struct header_tmp_value* _value = (struct header_tmp_value*)data;

  if (_value->need_free) {
    if (_value->ptr) {
      free((void*)_value->ptr);
      _value->ptr = NULL;
    }
  }
  free(data);
  data = NULL;
}

static void parse_message_header_sequence(const char* header_name, struct header_value* value, header_parse_array* array,
    uint32_t array_len, sip_header_gather* header_gather) {
  uint32_t iterator = 0;
  int      flag = NOT_FIND_FUNC;

  if (NULL == header_name || NULL == value || NULL == header_gather || NULL == array || array_len <= 0)
    return;

  for (iterator = 0; iterator < array_len; iterator++) {
    if (strcmp(header_name, array[iterator].header_name) == 0) {
      flag = FIND_FUNC;
      array[iterator].parse_header_by_name(value, header_gather);
      break;
    }
  }
  if (!flag)
    return;
}

static void parse_messsage_body_sequence(
    const char* key_str, body_value* value, body_parse_array* array, uint32_t array_len, sip_body_gather* body_gather) {
  uint32_t iterator = 0;
  int      flag = NOT_FIND_FUNC;

  if (NULL == key_str || NULL == value || NULL == array || NULL == body_gather || array_len <= 0)
    return;

  for (iterator = 0; iterator < array_len; iterator++) {
    if (strcmp(key_str, array[iterator].case_type) == 0) {
      flag = FIND_FUNC;
      array[iterator].parse_body_by_case(value, body_gather);
      break;
    }
  }
  if (!flag)
    return;
}

/* parse message_header by end_tag: 0x0d0a */
static void parse_sip_message_header(const uint8_t* payload, const uint16_t payload_len, struct sip_request_info* sip_info) {
  uint32_t a;
  uint16_t line_len = 0;
  uint8_t  parsed_lines = 0;
  //    uint8_t unknown_line_num = 0;
  uint16_t             end = payload_len - 1;
  const uint8_t*       line_ptr = payload;
  char*                _header;
  struct header_value* _value;

  int header_len;

  if ((payload_len == 0) || (payload == NULL) || (end == 0))
    return;

  sip_info->table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_header, _free_key_value);
  if (sip_info->table == NULL) {
    return;
  }

  for (a = 0; a < end; a++) {
    if (get_uint16_t(payload, a) == ntohs(0x0d0a)) {
      line_len = (uint16_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));
      //if(line_len>payload_len){printf("line 1714\n");return;}
      if (line_len == 0) {
        sip_info->empty_line_position = a;
        sip_info->PROTOCOL_VAL_LEN(message_header) =
            sip_info->empty_line_position - (sip_info->PROTOCOL_VAL_PTR(message_header) - payload) - 2;
        break;
      }

      _value = (struct header_value*)malloc(sizeof(struct header_value));
      if (!_value) {
        goto next_line;
      }
      _value->need_free = 0;

      if (parsed_lines == 0) {
        _header = strdup("head_line");
        if (!_header) {
          free(_value);
          goto next_line;
        }
        _value->len = line_len;
        _value->ptr = line_ptr;

        g_hash_table_insert(sip_info->table, _header, _value);

        sip_info->PROTOCOL_VAL_PTR(message_header) = line_ptr + line_len + 2;
      } else {
        //header_len = find_special_char(line_ptr, line_len, ':');
        header_len = find_special_colon_space(line_ptr, line_len);
        //if(header_len>payload_len){printf("line 1744\n");return;}
        if (header_len <= 0) {
          free(_value);
          goto next_line;
        }
        if (line_ptr[header_len - 1] == ' ')
          _header = strndup((const char*)line_ptr, header_len - 1);
        else
          _header = strndup((const char*)line_ptr, header_len);

        strdown_inplace(_header);

        if (header_len + 1 >= line_len) {
          free(_header);
          free(_value);
          goto next_line;
        }

        if (line_ptr[header_len + 1] == ' ') {
          _value->len = line_len - header_len - 2;
          _value->ptr = line_ptr + header_len + 2;
        } else {
          _value->len = line_len - header_len - 1;
          _value->ptr = line_ptr + header_len + 1;
        }
        if (g_hash_table_lookup(sip_info->table, _header)) {
          g_hash_table_remove(sip_info->table, _header);
        }
        g_hash_table_insert(sip_info->table, _header, _value);

        parse_message_header_sequence(_header, _value, _header_parse_array,
            sizeof(_header_parse_array) / sizeof(header_parse_array), &(sip_info->header_gather));
      }

    next_line:
      parsed_lines++;
      line_ptr = &payload[a + 2];
      line_len = 0;

      if ((a + 2) >= payload_len)
        break;

      a++; /* next char in the payload */
    }
  }
}

/* parse message_body by end_tag: 0x0d0a */
static void parse_sip_message_body(const uint8_t* payload, const uint16_t payload_len, struct sip_request_info* sip_info) {
  int            key_len = 0;
  int            a_attr_num = 0;
  uint16_t       body_len = 0;
  uint32_t       iterator = 0;
  const uint8_t* begin = payload;
  uint16_t       end = payload_len - 1;
  body_value*    _value;
  char*          key_str = NULL;

  if (NULL == payload || payload_len <= 0 || NULL == sip_info)
    return;

  if (NULL == sip_info->table) {
    return;
  }

  for (iterator = 0; iterator < end; iterator++) {
    if (get_uint16_t(payload, iterator) == ntohs(0x0d0a)) {
      body_len = (uint16_t)(((unsigned long)&payload[iterator]) - ((unsigned long)begin));

      _value = (body_value*)malloc(sizeof(body_value));
      if (!_value) {
        goto next_line;
      }
      _value->need_free = 0;

      key_len = find_special_char(begin, body_len, '=');
      if (key_len < 0) {
        free(_value);
        goto next_line;
      }
      //if(key_len>payload_len){printf("file:%s,func:%s,line:%d\n",__FILE__,__FUNCTION__,__LINE__);break;}
      key_str = strndup((const char*)begin, key_len);
      if (NULL == key_str) {
        free(_value);
        goto next_line;
      }

      _value->ptr = begin + 2;
      _value->len = body_len - 2;

      if (g_hash_table_lookup(sip_info->table, key_str)) {
        g_hash_table_remove(sip_info->table, key_str);
      }
      g_hash_table_insert(sip_info->table, key_str, _value);
      parse_messsage_body_sequence(
          key_str, _value, _body_parse_array, sizeof(_body_parse_array) / sizeof(body_parse_array), &(sip_info->body_gather));

    next_line:
      if (iterator + 2 > payload_len)
        break;

      begin = &payload[iterator + 2];
      body_len = 0;

      iterator++;
    }
  }
}

auto sipkeeper = SIPKEEPER;

SIPKeeper::SIPKeeper() {
  setRegCallback(IPC_PROTOCOL_SIP,"sip",
      std::bind(&SIPKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&SIPKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      NULL, NULL);
}

void SIPKeeper::identifyProto(flow_info* flow, uint8_t C2S, const uint8_t* payload, uint32_t payload_len) {
  int line_len = 0;

  line_len = find_packet_line_end(payload, payload_len);

  if (line_len < 9)
    return;

  if (strncasecmp((const char*)payload, "SIP/", 4) == 0 || strncasecmp((const char*)payload + line_len - 8, " SIP/", 5) == 0) {
    flow->real_protocol_id = IPC_PROTOCOL_SIP;
    return;
  }

  int  part_len = 256;
  char tmp_buff[256] = {0};

  memcpy(tmp_buff, payload, part_len - 1);
  const char* p = NULL;
  p = strcasestr(tmp_buff, "sip");
  if (p != NULL) {
    flow->real_protocol_id = IPC_PROTOCOL_SIP;
  }

  return;
}

void SIPKeeper ::dissectProto(flow_info* flow, uint8_t C2S, const uint8_t* payload, uint32_t payload_len) {
  uint8_t                 is_sip_request = 0;
  uint8_t                 is_sip_response = 0;
  uint16_t                uri_start;
  int                     line_len;
  uint32_t                sip_content_len;
  gpointer                _value;
  struct header_value*    value;
  struct sip_request_info sip_info;

  std::shared_ptr<SipStream> stream = nullptr;

  auto it = map_streams_.find(flow);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    auto newStreamPtr = std::make_shared<SipStream>(flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<SipStream>()");
      return;
    }
    stream = newStreamPtr;
    map_streams_[flow] = newStreamPtr;
  }

  memset(&sip_info, 0, sizeof(sip_info));
  if (payload_len <= 0 || payload == NULL)
    return;

  line_len = find_packet_line_end(payload, payload_len);
  if (line_len < 9)
    return;

  if (strncasecmp((const char*)payload + line_len - 8, " SIP/", 5) == 0) {
    is_sip_request = 1;
    C2S = 0;
  } else if (strncasecmp((const char*)payload, "SIP/", 4) == 0) {
    is_sip_response = 1;
    C2S = 1;
  }

  if (!is_sip_request && !is_sip_response)
    return;

  parse_sip_message_header(payload, payload_len, &sip_info);
  if (!sip_info.table) {
    return;
  }

  _value = g_hash_table_lookup(sip_info.table, "head_line");
  if (!_value) {
    g_hash_table_destroy(sip_info.table);

    return;
  }

  value = (struct header_value*)_value;
  if (is_sip_request && value->len > 0) {
    uri_start = find_blank_space(value->ptr, value->len);
    if (uri_start <= 0) {
      g_hash_table_destroy(sip_info.table);
      return;
    }

    sip_info.PROTOCOL_VAL_PTR(method) = value->ptr;
    sip_info.PROTOCOL_VAL_LEN(method) = uri_start;

    if (uri_start + 9 + 7 > value->len) {
      g_hash_table_destroy(sip_info.table);
      return;
    }

    sip_info.PROTOCOL_VAL_PTR(uri) = &value->ptr[uri_start + 1];
    sip_info.PROTOCOL_VAL_LEN(uri) = value->len - (uri_start + 9);

    sip_info.PROTOCOL_VAL_PTR(version) = sip_info.PROTOCOL_VAL_PTR(uri) + sip_info.PROTOCOL_VAL_LEN(uri) + 1;
    sip_info.PROTOCOL_VAL_LEN(version) = 7;
  }

  if (is_sip_response && value->len > 12) {
    sip_info.PROTOCOL_VAL_PTR(version) = value->ptr;
    sip_info.PROTOCOL_VAL_LEN(version) = 7;

    sip_info.PROTOCOL_VAL_PTR(status_code) = &value->ptr[8];
    sip_info.PROTOCOL_VAL_LEN(status_code) = line_len - 8;
  }

  stream->sdp_.setSipinfo(flow, sip_info);
  if (sip_info.empty_line_position > 0 && sip_info.empty_line_position < payload_len - 2) {
    sip_content_len = payload_len - sip_info.empty_line_position - 2;
    _value = g_hash_table_lookup(sip_info.table, "content-type");
    if (_value) {
      value = (struct header_value*)_value;
      if (value->len == strlen("application/sdp") && strncmp((const char*)value->ptr, "application/sdp", value->len) == 0) {
        parse_sip_message_body(&payload[sip_info.empty_line_position + 2], sip_content_len, &sip_info);
        flow_info* sip_flow = static_cast<flow_info*>(stream.get());
        stream->sdp_.dissect_sdp(sip_flow, C2S, &payload[sip_info.empty_line_position + 2], sip_content_len);
        if (stream->sdp_.conversion_flag_) {
          stream->ssrc = stream->sdp_.ssrc;
        }
      }
    }
  }
  g_hash_table_destroy(sip_info.table);

  return;
}