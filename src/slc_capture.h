
#ifndef __SLC_CAPTURE_H
#define __SLC_CAPTURE_H

#include <string>
#include <atomic>
#include <mutex>
#include <thread>

#include "ipc/ipc.h"
#include "ipc/ipc_msg.h"
#include "slc_config.h"
#include "plugin_loader.h"
#include "unix_socket.h"

// Forward declarations
class UnixSocketServerDgram;
class UnixSocketClientStream;
struct pcappkt_hdr {
    unsigned int tv_sec;      /* timestamp seconds */
    unsigned int tv_usec;     /* timestamp microseconds */
    unsigned int caplen;      /* number of octets of packet saved in file */
    unsigned int len;          /* actual length of packet */
};


enum YASLC_STATUS {
  YASLC_DISSECT,
  YASLC_NO,
  YASLC_YES,
};


typedef struct yaslc_status_t {
  IPC_PROTOCOL_TYPE type;
  YASLC_STATUS      status;
} yaslc_status_t;
// int update_yaSlc_status(yaslc_status_t update_status) ;
class SlcCap {
public:
  SlcCap(const std::string &lRecordCntPerFile, const std::string &plugin_path = "", const std::string &dump_path = "",const std::string &nalu_socket_path = "/run/nalu.sock", const std::string &control_socket_path = "/run/ctrl.sock");
  ~SlcCap();

public:
  int inspectPcapFile(const char *pPcapFileName);

  int  inspectLiveCap(const char *pStrIf);

  std::string GenerateCapFileName();

  int parseSlcCapture();
  void clearCapInfo() ;
  void setCapFileSwitch(int cap_switch);
  void setCapFileName(std::string filename) {
    // capFileName.clear();
    // capFileNameFinish.clear();
    capFileNameFinish = filename;
    capFileName = filename + ".writing";
   }

  // Unix Socket相关方法
  void sendNaluDataToClients(const uint8_t* nalu_data, uint32_t length);
  void sendStatusToClients(const yaslc_status_t& status);
  void sendHelloMessage();
  void sendStreamDetectedMessage(IPC_PROTOCOL_TYPE proto_type);

  // NALU Socket服务器相关方法
  void startNaluSocketServer();
  void stopNaluSocketServer();

  // 抓包控制相关方法
  void startCapture();
  void stopCapture();
  void startCaptureWithInfo(const std::string& brand, const std::string& model, uint32_t file_size_mb);
  bool isCapturing() const { return is_capturing_; }

  // 控制socket客户端相关方法
  void startControlClient();
  void stopControlClient();
  void sendCaptureStatus(bool capturing);

  // 心跳相关方法
  void startHeartbeat();
  void stopHeartbeat();
  void sendHeartbeat();

  void saveFile(const uint8_t *pPktData, int len);
  int  saveFileStart();
  void saveFileStop();

  FILE               *f = NULL;
  IPC_DECODER        *decoder_handle;
  PluginLoader       *plugin_loader_;

  // NALU Socket服务器
  UnixSocketServerDgram   *nalu_server_;
  std::string        nalu_socket_path_;

  // 控制socket客户端
  UnixSocketClientStream   *control_client_;
  std::string        control_socket_path_;

  // 抓包状态控制
  std::atomic<bool>  is_capturing_;
  std::mutex         capture_mutex_;

  // 心跳相关成员
  std::thread        heartbeat_thread_;
  std::atomic<bool>  heartbeat_running_;
  int                heartbeat_interval_;

private:
  static void onGotLivePkt(uint8_t *user, const struct pcap_pkthdr *h, const u_char *bytes);

  void parsePcapPkt(const timeval *pCapTs, int len, int pktNO, const uint8_t *pData);

private:
  std::string strSlcCapFilter_;

  //捕获文件参数
  bool        cap_switch_;
  FILE       *capFileFp = NULL;
  long        capFileSize_ = 0;
  long        capMaxFileSize_ = 0;
  std::string brand_;
  std::string model_;
  std::string capFileName;
  std::string capFileNameFinish;
  std::string filepath_;
};

#endif
