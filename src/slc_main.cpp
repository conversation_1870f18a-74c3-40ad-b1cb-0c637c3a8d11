#include "slc_capture.h"
#include "slc_config.h"
#include "common/tools.h"

#include <cstring>
#include <string>
std::string rtxdr_cap_filter;
std::string plugin_path;
std::string dump_file_path;
std::string nalu_socket_path = "/run/nalu.sock";
std::string control_socket_path = "/run/ctrl.sock";
bool enable_dump = false;
SlcCap* g_cap = nullptr;

void print_usage(const char* program_name) {
  printf("Usage: %s [OPTIONS]\n", program_name);
  printf("Options:\n");
  printf("  --plugin <path>    指定插件库路径 (默认: ./libipc.so)\n");
  printf("  --dump <path>      启用数据转储并指定输出文件路径\n");
  printf("  --nalu_socket <path>    指定Unix Socket路径 (默认: /tmp/yaslc.sock)\n");
  printf("  --ctrl_socket <path>    指定Unix Socket路径 (默认: /tmp/yaslc.sock)\n");
  printf("  --help, -h         显示此帮助信息\n");
  printf("\n");
  printf("Examples:\n");
  printf("  %s --plugin /path/to/libipc.so\n", program_name);
  printf("  %s --dump /tmp/capture.dump\n", program_name);
  printf("  %s --nalu_socket /tmp/custom.sock\n", program_name);
  printf("  %s --ctrl_socket /tmp/custom.sock\n", program_name);
  printf("  %s --plugin /path/to/libipc.so --dump /tmp/capture.dump --socket /tmp/custom.sock\n", program_name);
}

int main(int argc, char* argv[]) {
  // 解析命令行参数
  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "--plugin") == 0 && i + 1 < argc) {
      plugin_path = argv[i + 1];
      i++; // 跳过下一个参数
    } else if (strcmp(argv[i], "--dump") == 0 && i + 1 < argc) {
      dump_file_path = argv[i + 1];
      enable_dump = true;
      i++; // 跳过下一个参数
    } else if (strcmp(argv[i], "--nalu_socket") == 0 && i + 1 < argc) {
      nalu_socket_path = argv[i + 1];
      i++; // 跳过下一个参数
    } else if (strcmp(argv[i], "--ctrl_socket") == 0 && i + 1 < argc) {
      control_socket_path = argv[i + 1];
      i++; // 跳过下一个参数
    } else if (strcmp(argv[i], "--help") == 0 || strcmp(argv[i], "-h") == 0) {
      print_usage(argv[0]);
      return 0;
    } else {
      printf("Unknown option: %s\n", argv[i]);
      print_usage(argv[0]);
      return 1;
    }
  }

  // 创建抓包句柄
  g_cap = new SlcCap(rtxdr_cap_filter, plugin_path, dump_file_path,nalu_socket_path, control_socket_path);

  yaslc_status_t status;
  status.type = IPC_PROTOCOL_UNKNOWN;
  status.status = YASLC_DISSECT;
  // signal(SIGUSR1, onSIGUSR1);
  // signal(SIGUSR2, onSIGUSR2);

  // go ...
  if (CFG->eMode_LiveCap == RUNMODE) {  // live cap

    printf("live capture mode on IF %s \n", INTERFACE_NAME);
    g_cap->inspectLiveCap(INTERFACE_NAME);

  } else if (CFG->eMode_OfflinePcap == RUNMODE) {  // offline pcap
    printf("offline mode on dir %s \n", PCAP_FILE_DIR);
    forDirEntry(PCAP_FILE_DIR, [](const char *pDirEntryName, bool bIsDir) {
      if (bIsDir) {
        return 0;
      }
      while (1) {
        g_cap->inspectPcapFile(pDirEntryName);
      }
    return 0;
    });
  }

  // 清理资源
  delete g_cap;
  g_cap = nullptr;

  return 0;
}