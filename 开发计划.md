# 开发计划

## 目标

1.将插件修改为 dlopen 的方式加载，而不是通过 link_lib 的方式加载 

2.移除主程序中的 rtsp 服务器部分代码 

3.为主线程增加一个 unix socket `/run/nalu.sock`用于向应用程序发送解码的 nalu 帧 

4.为主程序增加 args --plugin 插件路径 --dump 抓包路径 如果启动参数中没有这些则通过配置文件读取默认配置 

4.添加一个事件处理线程 使用 unix socket `/run/ctrl.sock` 

5.事件处理线程使用 epoll 管理事件 

6.事件处理线程需要完成
   1.发送心跳 

   2.发送 hello 消息，用于通知程序已经启动 

   3.发送探测成功消息 

   4.接收抓包开始消息 

   5.接收抓包停止消息
7.优化程序，使程序的内存占用减小

8.心跳中未实现是否成功解析的消息

 
## 说明

1.使用 unix domain socket 进行通信，使用 dgram 类型; 由解析程序创建，应用管理连接上来接收 nalu、心跳包并进行控制等； 

2.使用 /run/ctrl.sock 进行信令控制； 

3.使用 /run/nalu.sock 传输 nalu ； 

4.所有消息由 8 字节消息头与消息体两部分组成，消息头中有事务 id 每发送一条消息进行递增，关联类消息使用相同的事务 id;

```c
enum ipc_msg_type_enum
{
    IPC_MSG_NONE = 0
    IPC_MSG_HELLO,
    IPC_MSG_HEART_BEAT,
    IPC_MSG_STREAM_DETECTED,
    IPC_MSG_STREAM_NALU,
    IPC_MSG_DUMP_START,
    IPC_MSG_DUMP_STOP,       // 不需要消息体
    IPC_MSG_DUMP_DONE,       // 不需要消息体
};

消息头：
struct ipc_msg_hdr
{
    uint8_t  proto_version;   // 10, v1.0
    uint8_t  msg_type;        // 消息类型, 取值为 ipc_msg_type_enum
    uint16_t transaction_id;  // 用于配对请求与响应
    uint32_t msg_len;         // 不含消息头；
};

hello 消息：
struct ipc_msg_hello
{
    uint8_t dpi_version;      // 解析程序版本，例如 24 表示 v2.4;
    uint8_t plugin_version;   // 插件版本号；
    char    plugin_path[128]; // 当前加载的插件路径
};

心跳消息：
struct ipc_msg_heart_beat
{
    uint8_t dpi_status;       // 状态：idle(无流量), probing(探测中), ok(正常解析), error(出错)
    uint8_t stream_cnt;
    char    msg[128];         // 补充消息，例如出错信息
};

struct ipc_msg_stream_detected
{
    uint32_t streamID;
    uint32_t flag;             // 预留标志字段，暂时不使用，是否有音频等；
    char     proto[16];        // ipc 中用于传输该视频的协议，例如 'rtsp/rtp', 'dahua'
};

struct ipc_msg_stream_nalu
{
    uint32_t streamID;          // stream 标识
    uint32_t nalu_seq;          // nalu 序号
    uint8_t  nalu_payload[0];   // nalu
};

struct ipc_msg_dump_start
{
    uint32_t dump_size;        // 抓包文件大小，单位为字节；
    char brand[64];            // ipc 品牌名，用于编码到 pcap 文件名上；
    char serial[64];           // ipc 型号，用于编码到 pcap 文件名上；
};
```
