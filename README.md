# surveillance camera 项目

---

## 构建

---

### 在 amd64 平台下编译方法

编译方法

```
mkdir -p build ; cmake3 -B build && make -j -C build
```

注：log 模块需要使用 gcc 9 版本才能编译成功

启用方法

```
yumi install devtoolset-9
scl enable devtoolset-9 bash
```

### 在 Ubuntu 环境下交叉编译 ARMv7-OpenWrt 环境可运行的程序

将程序目录拷贝到 OpenWrt 源码目录下的 package 目录下

目录结构为

```makefile
> .
> ├── Src_NorFlash
> │   ├── package
>            ├── libipc
>                  ├── Makefile
>                  └── src
```

其中 MakeFile 的格式为

```
#
# Copyright (C) 2006-2016 OpenWrt.org
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

include $(TOPDIR)/rules.mk

PKG_NAME:=libipc
PKG_VERSION:=1.0
PKG_RELEASE:=1


# PKG_BUILD_PARALLEL:=1
# PKG_INSTALL:=1

include $(INCLUDE_DIR)/package.mk
include $(INCLUDE_DIR)/cmake.mk
define Package/libipc
	SECTION:=Utilities
	TITLE:=libipc
	CATEGORY:=Utilities
	DEPENDS:=+!USE_MUSL:libthread-db +libstdcpp +libpcap +glib2 +libiconv
endef

define Package/libipc/description
libipc libipc
endef

define Package/libipc/install
	$(INSTALL_DIR) $(1)/dev/shm/libipc
	$(CP) $(PKG_BUILD_DIR)/run/* $(1)/dev/shm/libipc/

	$(INSTALL_DIR) $(1)/$(INSTALL_BINDIR)
	$(CP) $(PKG_BUILD_DIR)/run/* $(1)/$(INSTALL_BINDIR)/
endef




CXX_FLAGS+= \
	    -O0 -g

CXXFLAGS+= -g

CONFIGURE_VARS+= \
	ac_cv_search_tgetent="$(TARGET_LDFLAGS) -lncurses -lreadline"

TARGET_LDFLAGS+= \
	$(INTL_LDFLAGS) $(if $(INTL_FULL),-lintl) \
	-static-libstdc++ \

$(eval $(call BuildPackage,libipc))


```

src 目录内为真正的工程目录，目录结构为

```
> .
> ├── Src_NorFlash
> │   ├── package
>            ├── libipc
>                  ├── Makefile
>                  └── src
>                       ├── build
>                       ├── cmake
>                       ├── etc
>                       ├── include
>                       ├── lib
>                       ├── run
>                       ├── src
>                       └── CMakeLists.txt
```

`注：在arm下编译，需要将lib/arm/目录下的库文件替换到lib目录下`
编译命令：

```
make menuconfig
```

出现图形化界面，在图形化界面中选中 yaslc 工程（Utilities 的选项中，可配置在哪个目录中，makefile 中配置）

勾选为 [*] libipc
按 exit 两次退出,默认保存为.config
在 openwrt 源码根目录下输入

```
rm build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0/* -rf&& make package/libipc/compile V=s
```

单独编译 yaslc 工程
`/compile`的含义为 编译 yaslc/目录下的 compile target 含义等同于在 build 目录中 make compile
此时，openwrt 的 make 工具会将代码拷贝进入 arm 环境下的路径 build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0 目录中进行自动编译

编译出的二进制文件也会生成在该目录下

## 调试

#### arm 中调试方法
安上述编译步骤，勾选 devel 中的 gdb，编译 gdb

```
make package/devel/gdb/compile V=s
```

gdb 中包含 gdb 与 gdbserver 两个可执行文件

在`build_dir/target-arm_cortex-a7_musl_eabi/gdb-10.1/gdbserver/`目录下找到 gdbserver 二进制可执行文件，拷贝到 arm 板上，执行

```
./gdbserver 127.0.0.1:3000 VideoEncode
```

启动 gdbserver

在编译机 opwrt 源码目录下`staging_dir/toochain-arm_cortex-a7_gcc-8.4.0_musl_eabi/bin`
中找到`arm-openwrt-linux-gdb`可执行文件，运行

```
./arm-openwrt-linux-gdb
target remote 开发板ip:端口
```

可以远程连接开发板进行调试
gdbserver 会默认运行到入口函数暂停
执行

```
c
```

启动程序，其余操作与 amd64 平台相同

#### 将编译出的可执行文件剥离符号

```
staging_dir/toolchain-arm_cortex-a7_gcc-8.4.0_musl_eabi/bin/arm-openwrt-linux-strip build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0/run/yaslc_0.0.1
```

### 配置开发板开机启动程序

1 将 release 版本程序与配置文件拷贝到开发板 root 目录下
2 配置`/etc/rc.local`文件

```
# Put your custom commands here that should be executed once
# the system init finished. By default this file does nothing.

echo 3 > /proc/sys/vm/drop_caches

if [ -e /sbin/boot_times.sh ]; then
        /sbin/boot_times.sh  &
fi
sleep 10
echo starting >> /root/start.log
/bin/date >> /root/start.log
# 配置网卡镜像操作
ip link set up dev lan1
tc qdisc add dev lan2 clsact
tc filter add dev lan2 ingress matchall skip_sw action mirred egress mirror dev lan1
tc filter add dev lan2 egress matchall skip_sw action mirred egress mirror dev lan1
ethtool -K "eth0" gso off gro off tso off
# 启动程序
/root/yaslc_0.0.1

echo starting_finish >> /root/start.log
/bin/date >> /root/start.log
exit 0
```

## 设计

version 0.0 ： 通过 main 程序启动

### 主线程

在程序启动时 协议解析器注册到 [g_RegDissectCallbackMap] 中
只实现了 Rtp 协议
构造抓包类
启动发送线程

### 解析线程

libpcap 读取网卡流量 或 读取离线 pcap 文件 [SlcCapture] 类集成 libpcap

[SLCPARSER] 类 解析到 ip 层 通过注册的协议解析器 遍历识别

当识别成功 进入解析过程
[RtpKeeper] 类 自身管理一个 [map_streams_] 的表
表结构为

```
std::map<flow_info *, std::shared_ptr<RtpStream>>
```

通过 flow_info \* 直接将 rtp 的 payload 送入 [RtpStream]类

[RtpStream]中维护自己的[Unpacker_] (设计缺陷 只支持 h264)
[RtpStream]中还存在一个存放 NALU 结构的队列类[SlcNaluqueue]

[SlcNaluqueue]可以通过接口入队与出队

[Unpacker_]为类型为 [RtpH264Unpacker] 的类
[Unpacker_]注册了[SlcNaluqueue]入队的回调接口

[RtpStream]直接将 rtp 的 payload 转送给 [RtpH264Unpacker]

当 [RtpH264Unpacker]解析完一帧完整的 NALU 结构时 调用回调函数
[NaluCallback] 将 nalu 结构存入[RtpStream].[SlcNaluqueue]

### 发送线程

[SendFrameThread] 线程从 [RtpKeeper].[map_streams_] 中遍历流 然后通过 [RtpStream].[SlcNaluqueue]的出队接口，不断出队
出队后 通过[RtpH264Packer]打包成完整的 rtp，在进行发送

version 0.1 ： 添加了 RTSP 服务器

### 主线程

构造 rtsp 服务器

### 发送线程

当收到 rtsp 获取视频流时 才开始发送

version 0.2 ： 添加以接口形式

将程序抽象成以下的层级

接口层

[ipc_config]
[ipc_decoder]

公共解析层(框架层)

[SLCPARSER]

协议解析层

[RTPKEEPER]
[RTSPKEEPER]
[DHAVKEEPER]

解码层

### [h264Unpacker]

接口层需要实现 送入网络流量 通过回调函数的方式将 nalu 返回给注册的函数

### 增加新协议需要完成的接口

src/src/proto 目录为存放协议解析文件
以 slc_dhav 为例子：
定义一个协议解析类（单例），用于管理协议流（proto_stream）
解析类
slc_dhav.h

```cpp
#define DHAVKEEPER DHAVKeeper::GetInstance()
//此处的继承都是需要的，用于进行协议管理
class DHAVKeeper : public ObserverSessionKeeper, public singleton<DHAVKeeper> {
  friend class SlcProtoLoader<DHAVKeeper>;

public:
  DHAVKeeper();//需要在构造中调用回调注册函数将自身注册到协议初始化中
  void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);//必须实现,协议识别函数


  //挑选一个进行具体实现，必须将两个函数都进行绑定，在绑定时可以绑定NULL
  //tcp重组发生在框架层，dissectProto_rsm到达的报文一定是tcp有序报文
  void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);//不需要tcp重组的解析函数
  int  dissectProto_rsm(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);//需要tcp重组的解析函数
  int  miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);      //需要重组的丢包通知

  std::map<flow_info *, std::shared_ptr<DHAVStream>> map_streams_;//管理流的map
};
```

slc_dhav.cpp

```cpp
auto dhavkeeper = DHAVKEEPER;
DHAVKeeper::DHAVKeeper() {
  setRegCallback(IPC_PROTOCOL_DHAV,"dhav",
      //识别函数 必须
      std::bind(&DHAVKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      //不需要重组的解析函数
      std::bind(&DHAVKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      //需要重组的解析函数
      std::bind(&DHAVKeeper::dissectProto_rsm, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4, std::placeholders::_5, std::placeholders::_6, std::placeholders::_7),
      //需要重组的丢包通知
      std::bind(
          &DHAVKeeper::miss, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
}
```

    附：可以绑定空解析函数的解析类
    ```cpp
      RtpKeeper::RtpKeeper() {
        setRegCallback(IPC_PROTOCOL_RTP, "rtp",
            //识别函数 必须
            std::bind(&RtpKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
                std::placeholders::_4),
            //不需要重组的解析函数
            std::bind(&RtpKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
                std::placeholders::_4),
            //需要重组的解析函数 空
            NULL,
            //需要重组的丢包通知 空
            NULL);
      }
    ```

协议 stream 类
此类为协议解析的实现

```cpp
class DHAVStream : flow_info {
public:
}
```

继承 flow_info 是为了方便传递五元组

在协议 stream 类中调用

```cpp
  flow_info *stream = (flow_info *)(this);
  if (stream->user) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(stream->getUserData());

    decoder->onNaluCallback(naluType, nalu, naluLen, (void *)stream);
  }
```

来实现解码后的视频流送回 slc_capture.cpp:128 的发送到 rtsp 客户端

在现有的设计中，协议解析部分是放在 stream 类中实现的 ，在 `协议解析类` 中实现也是可以的

### 应用与解析解耦

1.使用 unix domain socket 进行通信，使用 dgram 类型; 由应用程序创建，应用管理连接上来接收 nalu、心跳包并进行控制等；
2.使用 /run/ctrl.sock 进行信令控制；
3.使用 /run/nalu.sock 传输 nalu ；
4.所有消息由 8 字节消息头与消息体两部分组成，消息头中有事务 id 每发送一条消息进行递增，关联类消息使用相同的事务 id;

#### 架构

测试程序 (服务器端) slc 程序 (客户端)
├── control*server* (DGRAM) ←→ control*client* (DGRAM)
│ 绑定到: /run/ctrl.sock 绑定到: /run/ctrl*cli.sock
└── nalu_server* (DGRAM) ←→ nalu*client* (DGRAM)  
 绑定到: /run/nalu.sock 绑定到: /run/nalu_cli.sock

#### 消息设定

```c
/**
 * @brief IPC消息类型枚举
 */
enum ipc_msg_type_enum
{
    IPC_MSG_NONE = 0,
    IPC_MSG_HELLO,           // hello消息，程序启动时发送
    IPC_MSG_HEART_BEAT,      // 心跳消息
    IPC_MSG_STREAM_DETECTED, // 流探测成功消息
    IPC_MSG_STREAM_NALU,     // NALU数据消息
    IPC_MSG_DUMP_START,      // 抓包开始消息
    IPC_MSG_DUMP_STOP,       // 抓包停止消息（不需要消息体）
    IPC_MSG_DUMP_DONE,       // 抓包完成消息（不需要消息体）
};

/**
 * @brief IPC消息头结构体
 * 所有消息都以此结构体开头
 */
struct ipc_msg_hdr
{
    uint8_t  proto_version;   // 协议版本，10表示v1.0
    uint8_t  msg_type;        // 消息类型，取值为ipc_msg_type_enum
    uint16_t transaction_id;  // 事务ID，用于配对请求与响应
    uint32_t msg_len;         // 消息体长度，不包含消息头
} __attribute__((packed));

/**
 * @brief Hello消息体
 * 程序启动时发送，通知应用程序已经启动
 */
struct ipc_msg_hello
{
    uint8_t dpi_version;      // 解析程序版本，例如24表示v2.4
    uint8_t plugin_version;   // 插件版本号
    char    plugin_path[128]; // 当前加载的插件路径
} __attribute__((packed));

/**
 * @brief 心跳消息体
 * 定期发送，报告程序状态
 */
struct ipc_msg_heart_beat
{
    uint8_t dpi_status;       // 状态：idle(无流量), probing(探测中), ok(正常解析), error(出错)
    uint8_t stream_cnt;       // 当前流数量
    char    msg[128];         // 补充消息，例如出错信息
} __attribute__((packed));

/**
 * @brief 流探测成功消息体
 * 当检测到新的视频流时发送
 */
struct ipc_msg_stream_detected
{
    uint32_t streamID;        // 流标识符
    uint32_t flag;            // 预留标志字段，暂时不使用，是否有音频等
    char     proto[16];       // 传输协议，例如'rtsp/rtp', 'dahua'
} __attribute__((packed));

/**
 * @brief NALU数据消息体
 * 传输解码后的NALU帧数据
 */
struct ipc_msg_stream_nalu
{
    uint32_t streamID;        // 流标识符
    uint32_t nalu_seq;        // NALU序号
    uint8_t  nalu_payload[0]; // NALU数据，变长
} __attribute__((packed));

/**
 * @brief 抓包开始消息体
 * 接收到抓包开始指令时的消息
 */
struct ipc_msg_dump_start
{
    uint32_t dump_size;       // 抓包文件大小，单位为字节
    char     brand[64];       // IPC品牌名，用于编码到pcap文件名上
    char     serial[64];      // IPC型号，用于编码到pcap文件名上
} __attribute__((packed));

/**
 * @brief DPI状态枚举
 * 用于心跳消息中的dpi_status字段
 */
enum dpi_status_enum
{
    DPI_STATUS_IDLE = 0,      // 空闲状态，无流量
    DPI_STATUS_PROBING,       // 探测中
    DPI_STATUS_OK,            // 正常解析
    DPI_STATUS_ERROR,         // 出错状态
};

/**
 * @brief 完整的IPC消息结构体
 * 包含消息头和消息体的联合体
 */
struct ipc_message
{
    struct ipc_msg_hdr hdr;
    union {
        struct ipc_msg_hello         hello;
        struct ipc_msg_heart_beat    heart_beat;
        struct ipc_msg_stream_detected stream_detected;
        struct ipc_msg_stream_nalu   stream_nalu;
        struct ipc_msg_dump_start    dump_start;
        uint8_t raw_data[0];         // 原始数据访问
    } body;
} __attribute__((packed));

```
